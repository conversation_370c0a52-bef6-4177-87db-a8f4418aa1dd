 
service: auth-clear-infra

plugins:
  - serverless-iam-roles-per-function
  - serverless-export-env
  - serverless-offline
  # - serverless-domain-manager

provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'dev'}
  region: us-east-1
  timeout: 30
  environment:
    STAGE: ${self:provider.stage}
    # ===================================================================
    # DATABASE ENVIRONMENT VARIABLES TEMPORARILY DISABLED
    # ===================================================================
    # Database connection info has been commented out to disable database
    # functionality. To re-enable: uncomment the variables below
    # ===================================================================
    # DB_ENDPOINT: !GetAtt AuroraCluster.Endpoint.Address
    # DB_PORT: !GetAtt AuroraCluster.Endpoint.Port
    # DB_NAME: authcleardb
    # DB_SECRET_ARN: !Ref AuroraSecret
  
  # ===================================================================
  # DATABASE-RELATED IAM PERMISSIONS TEMPORARILY DISABLED
  # ===================================================================
  # Database and VPC permissions have been commented out to disable
  # database functionality. To re-enable: uncomment the permissions below
  # ===================================================================
  # IAM permissions
  # iam:
  #   role:
  #     statements:
  #       - Effect: Allow
  #         Action:
  #           - secretsmanager:GetSecretValue
  #         Resource: !Ref AuroraSecret
  #       # VPC permissions
  #       - Effect: Allow
  #         Action:
  #           - ec2:CreateNetworkInterface
  #           - ec2:DescribeNetworkInterfaces
  #           - ec2:DeleteNetworkInterface
  #           - ec2:AssignPrivateIpAddresses
  #           - ec2:UnassignPrivateIpAddresses
  #         Resource: "*"

  # ===================================================================
  # VPC CONFIGURATION TEMPORARILY DISABLED
  # ===================================================================
  # VPC configuration has been commented out since it's primarily needed
  # for database access. To re-enable: uncomment the configuration below
  # ===================================================================
  # VPC configuration
  # vpc:
  #   securityGroupIds:
  #     - !Ref LambdaSecurityGroup
  #   subnetIds:
  #     - !Ref PrivateSubnetA
  #     - !Ref PrivateSubnetB

resources:
  Resources:
    # VPC configuration
    MainVPC:
      Type: AWS::EC2::VPC
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsSupport: true
        EnableDnsHostnames: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-vpc

    # Internet Gateway
    InternetGateway:
      Type: AWS::EC2::InternetGateway
      Properties:
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-igw

    # Internet Gateway Attachment
    InternetGatewayAttachment:
      Type: AWS::EC2::VPCGatewayAttachment
      Properties:
        InternetGatewayId: !Ref InternetGateway
        VpcId: !Ref MainVPC

    # Public Subnet (single)
    PublicSubnet:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref MainVPC
        AvailabilityZone: ${self:provider.region}a
        CidrBlock: ********/24
        MapPublicIpOnLaunch: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-public-subnet

    # Private Subnet A (Lambda)
    PrivateSubnetA:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref MainVPC
        AvailabilityZone: ${self:provider.region}a
        CidrBlock: ********/24
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-lambda-subnet-a

    # Private Subnet B (Lambda)
    PrivateSubnetB:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref MainVPC
        AvailabilityZone: ${self:provider.region}b
        CidrBlock: ********/24
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-lambda-subnet-b

    # ===================================================================
    # DATABASE SUBNETS TEMPORARILY DISABLED
    # ===================================================================
    # Database-specific subnets have been commented out to disable database
    # functionality. To re-enable: uncomment the subnets below
    # ===================================================================
    # Private Subnet C (Database)
    # DBPrivateSubnetA:
    #   Type: AWS::EC2::Subnet
    #   Properties:
    #     VpcId: !Ref MainVPC
    #     AvailabilityZone: ${self:provider.region}a
    #     CidrBlock: 10.0.5.0/24
    #     Tags:
    #       - Key: Name
    #         Value: ${self:service}-${self:provider.stage}-db-subnet-a
    #
    # Private Subnet D (Database)
    # DBPrivateSubnetB:
    #   Type: AWS::EC2::Subnet
    #   Properties:
    #     VpcId: !Ref MainVPC
    #     AvailabilityZone: ${self:provider.region}b
    #     CidrBlock: 10.0.6.0/24
    #     Tags:
    #       - Key: Name
    #         Value: ${self:service}-${self:provider.stage}-db-subnet-b

    # Route Tables
    PublicRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref MainVPC
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-public-rt

    # Public Route
    PublicRoute:
      Type: AWS::EC2::Route
      DependsOn: InternetGatewayAttachment
      Properties:
        RouteTableId: !Ref PublicRouteTable
        DestinationCidrBlock: 0.0.0.0/0
        GatewayId: !Ref InternetGateway

    # Public Subnet Route Table Association
    PublicSubnetRouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref PublicRouteTable
        SubnetId: !Ref PublicSubnet

    # NAT Gateway Elastic IP
    NatGatewayEIP:
      Type: AWS::EC2::EIP
      DependsOn: InternetGatewayAttachment
      Properties:
        Domain: vpc

    # NAT Gateway 
    NatGateway:
      Type: AWS::EC2::NatGateway
      Properties:
        AllocationId: !GetAtt NatGatewayEIP.AllocationId
        SubnetId: !Ref PublicSubnet
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-nat

    # Private Lambda Route Table
    LambdaRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref MainVPC
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-lambda-rt

    # Private Lambda Route
    LambdaRoute:
      Type: AWS::EC2::Route
      Properties:
        RouteTableId: !Ref LambdaRouteTable
        DestinationCidrBlock: 0.0.0.0/0
        NatGatewayId: !Ref NatGateway

    # Private Subnet A Route Table Association
    PrivateSubnetARouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref LambdaRouteTable
        SubnetId: !Ref PrivateSubnetA

    # Private Subnet B Route Table Association
    PrivateSubnetBRouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref LambdaRouteTable
        SubnetId: !Ref PrivateSubnetB

    # ===================================================================
    # DATABASE ROUTE TABLES TEMPORARILY DISABLED
    # ===================================================================
    # Database route tables have been commented out to disable database
    # functionality. To re-enable: uncomment the route tables below
    # ===================================================================
    # DB Route Table
    # DBRouteTable:
    #   Type: AWS::EC2::RouteTable
    #   Properties:
    #     VpcId: !Ref MainVPC
    #     Tags:
    #       - Key: Name
    #         Value: ${self:service}-${self:provider.stage}-db-rt

    # DB Subnet A Route Table Association
    # DBSubnetARouteTableAssociation:
    #   Type: AWS::EC2::SubnetRouteTableAssociation
    #   Properties:
    #     RouteTableId: !Ref DBRouteTable
    #     SubnetId: !Ref DBPrivateSubnetA

    # DB Subnet B Route Table Association
    # DBSubnetBRouteTableAssociation:
    #   Type: AWS::EC2::SubnetRouteTableAssociation
    #   Properties:
    #     RouteTableId: !Ref DBRouteTable
    #     SubnetId: !Ref DBPrivateSubnetB

    # Lambda Security Group
    LambdaSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for Lambda functions
        VpcId: !Ref MainVPC
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-lambda-sg

    # ===================================================================
    # AURORA SECURITY GROUP TEMPORARILY DISABLED
    # ===================================================================
    # Aurora security group has been commented out to disable database
    # functionality. To re-enable: uncomment the security group below
    # ===================================================================
    # Aurora Security Group
    # AuroraSecurityGroup:
    #   Type: AWS::EC2::SecurityGroup
    #   Properties:
    #     GroupDescription: Security group for Aurora PostgreSQL cluster
    #     VpcId: !Ref MainVPC
    #     SecurityGroupIngress:
    #       - IpProtocol: tcp
    #         FromPort: 5432
    #         ToPort: 5432
    #         SourceSecurityGroupId: !Ref LambdaSecurityGroup
    #       # Session Manager Host access will be added by session-manager stack
    #     Tags:
    #       - Key: Name
    #         Value: ${self:service}-${self:provider.stage}-aurora-sg

    # ===================================================================
    # DATABASE SUBNET GROUP AND SECRETS TEMPORARILY DISABLED
    # ===================================================================
    # Database subnet group and secrets have been commented out to disable
    # database functionality. To re-enable: uncomment the resources below
    # ===================================================================
    # DB Subnet Group
    # DBSubnetGroup:
    #   Type: AWS::RDS::DBSubnetGroup
    #   Properties:
    #     DBSubnetGroupDescription: Subnet group for Aurora database
    #     SubnetIds:
    #       - !Ref DBPrivateSubnetA
    #       - !Ref DBPrivateSubnetB
    #     Tags:
    #       - Key: Name
    #         Value: ${self:service}-${self:provider.stage}-db-subnet-group
    #
    # Aurora Secret (for database credentials)
    # AuroraSecret:
    #   Type: AWS::SecretsManager::Secret
    #   Properties:
    #     Name: ${self:service}/${self:provider.stage}/aurora-credentials
    #     Description: Aurora database credentials
    #     GenerateSecretString:
    #       SecretStringTemplate: '{"username": "postgres"}'
    #       GenerateStringKey: "password"
    #       PasswordLength: 16
    #       ExcludeCharacters: '"@/\'

    # Admin Invite Code Parameter
    # AdminInviteCodeParameter:
    #   Type: AWS::SSM::Parameter
    #   Properties:
    #     Name: /auth-clear-infra/${self:provider.stage}/admin-invite-code
    #     Type: String
    #     Value: PLATFORM_ADMIN_2024_${self:provider.stage}
    #     Description: Invite code required for platform admin registration

    # ===================================================================
    # AURORA DATABASE CLUSTER TEMPORARILY DISABLED
    # ===================================================================
    # Aurora PostgreSQL cluster and instance have been commented out to
    # disable database functionality. To re-enable: uncomment the resources below
    # ===================================================================
    # Aurora PostgreSQL Cluster with Serverless v2
    # AuroraCluster:
    #   Type: AWS::RDS::DBCluster
    #   Properties:
    #     Port: 5432
    #     Engine: aurora-postgresql
    #     EngineVersion: 13.9
    #     DatabaseName: authcleardb
    #     MasterUsername: !Join ['', ['{{resolve:secretsmanager:', !Ref AuroraSecret, ':SecretString:username}}' ]]
    #     MasterUserPassword: !Join ['', ['{{resolve:secretsmanager:', !Ref AuroraSecret, ':SecretString:password}}' ]]
    #     DBSubnetGroupName: !Ref DBSubnetGroup
    #     VpcSecurityGroupIds:
    #       - !Ref AuroraSecurityGroup
    #     ServerlessV2ScalingConfiguration:
    #       MinCapacity: 0.5
    #       MaxCapacity: 4.0
    #     BackupRetentionPeriod: 7
    #     DeletionProtection: false
    #     Tags:
    #       - Key: Name
    #         Value: ${self:service}-${self:provider.stage}-aurora-cluster
    #
    # Aurora PostgreSQL Instance
    # AuroraDBInstance:
    #   Type: AWS::RDS::DBInstance
    #   Properties:
    #     DBInstanceClass: db.serverless
    #     Engine: aurora-postgresql
    #     DBClusterIdentifier: !Ref AuroraCluster
    #     PubliclyAccessible: false
    #     DBInstanceIdentifier: ${self:service}-${self:provider.stage}-instance
    #     Tags:
    #       - Key: Name
    #         Value: ${self:service}-${self:provider.stage}-aurora-instance

  Outputs:
    VpcId:
      Value: !Ref MainVPC
      Export:
        Name: ${self:service}-${self:provider.stage}-vpc-id

    PrivateSubnetA:
      Value: !Ref PrivateSubnetA
      Export:
        Name: ${self:service}-${self:provider.stage}-private-subnet-a

    PrivateSubnetB:
      Value: !Ref PrivateSubnetB
      Export:
        Name: ${self:service}-${self:provider.stage}-private-subnet-b

    # ===================================================================
    # DATABASE-RELATED OUTPUTS TEMPORARILY DISABLED
    # ===================================================================
    # Database-related outputs have been commented out to disable database
    # functionality. To re-enable: uncomment the outputs below
    # ===================================================================
    # DBPrivateSubnetA:
    #   Value: !Ref DBPrivateSubnetA
    #   Export:
    #     Name: ${self:service}-${self:provider.stage}-db-private-subnet-a
    #
    # DBPrivateSubnetB:
    #   Value: !Ref DBPrivateSubnetB
    #   Export:
    #     Name: ${self:service}-${self:provider.stage}-db-private-subnet-b
    #
    # AuroraSecretArn:
    #   Value: !Ref AuroraSecret
    #   Export:
    #     Name: ${self:service}-${self:provider.stage}-db-secret-arn
    #
    LambdaSecurityGroupId:
      Value: !Ref LambdaSecurityGroup
      Export:
        Name: ${self:service}-${self:provider.stage}-lambda-sg-id

    # AuroraSecurityGroupId:
    #   Value: !Ref AuroraSecurityGroup
    #   Export:
    #     Name: ${self:service}-${self:provider.stage}-aurora-sg-id
    #
    # AuroraClusterEndpoint:
    #   Value: !GetAtt AuroraCluster.Endpoint.Address
    #   Export:
    #     Name: ${self:service}-${self:provider.stage}-aurora-endpoint
    #
    # AuroraClusterPort:
    #   Value: !GetAtt AuroraCluster.Endpoint.Port
    #   Export:
    #     Name: ${self:service}-${self:provider.stage}-aurora-port

    PublicSubnet:
      Value: !Ref PublicSubnet
      Export:
        Name: ${self:service}-${self:provider.stage}-public-subnet

package:
  individually: true
  exclude:
    - node_modules/**
  include:
    - src/** 