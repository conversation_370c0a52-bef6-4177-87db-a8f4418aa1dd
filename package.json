{"name": "auth-clear", "version": "1.0.0", "description": "auth-clear", "main": "handler.js", "type": "module", "scripts": {"_comment_database_scripts": "===== DATABASE-RELATED SCRIPTS TEMPORARILY DISABLED =====", "_typeorm": "cd functions/src/migration && tsx ../../../node_modules/typeorm/cli.js", "export-env": "cd functions && serverless export-env --stage dev --aws-profile payrix", "generate-env": "./scripts/generate-env.sh dev payrix", "_migration:generate": "npm run typeorm -- migration:generate -d ../data-source/index.ts", "_migration:run": "npm run typeorm -- migration:run -d ../data-source/index.ts", "_migration:revert": "npm run typeorm -- migration:revert -d ../data-source/index.ts", "_user:create-admin": "npx tsx scripts/create-admin-user.ts", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1", "deploy:infra": "cd infra && serverless deploy --stage dev --aws-profile payrix", "deploy:session-manager": "cd session-manager && serverless deploy --stage dev --aws-profile payrix", "deploy:functions": "cd functions && serverless deploy --stage dev --aws-profile payrix", "deploy:frontend": "cd frontend && serverless deploy --stage dev --aws-profile payrix && npm run update-cloudfront-id && npm run deploy", "deploy:frontend:files": "cd frontend && npm run deploy", "start:frontend": "cd frontend && npm run dev", "deploy:all": "npm run deploy:infra && npm run deploy:frontend && npm run deploy:functions", "function:create-merchant": "cd functions && serverless deploy function -f createMerchant --stage dev --aws-profile payrix", "function:list-merchants": "cd functions && serverless deploy function -f listMerchants --stage dev --aws-profile payrix", "function:create-admin": "cd functions && serverless deploy function -f createAdmin --stage dev --aws-profile payrix", "function:login": "cd functions && serverless deploy function -f loginUser --stage dev --aws-profile payrix", "logs:create-admin": "cd functions && serverless logs -f createAdmin --stage dev --aws-profile payrix --tail", "logs:login": "cd functions && serverless logs -f loginUser --stage dev --aws-profile payrix --tail", "logs:create-merchant": "cd functions && serverless logs -f createMerchant --stage dev --aws-profile payrix --tail", "logs:list-merchants": "cd functions && serverless logs -f listMerchants --stage dev --aws-profile payrix --tail", "token:dev:create": "npx tsx scripts/manage-tokens.ts create-token --stage dev --profile payrix", "token:test:create": "npx tsx scripts/manage-tokens.ts create-token --stage test --profile payrix", "token:prod:create": "npx tsx scripts/manage-tokens.ts create-token --stage prod --profile payrix", "token:dev:rotate": "npx tsx scripts/manage-tokens.ts rotate-token --stage dev --profile payrix", "token:test:rotate": "npx tsx scripts/manage-tokens.ts rotate-token --stage test --profile payrix", "token:prod:rotate": "npx tsx scripts/manage-tokens.ts rotate-token --stage prod --profile payrix", "remove:functions": "cd functions && serverless remove --stage dev --aws-profile payrix", "remove:session-manager": "cd session-manager && serverless remove --stage dev --aws-profile payrix", "remove:infra": "cd infra && serverless remove --stage dev --aws-profile payrix", "remove:frontend": "cd frontend && aws s3 rm s3://auth-clear-frontend-dev --recursive --profile payrix && serverless remove --stage dev --aws-profile payrix", "remove:all": "npm run remove:functions && npm run remove:frontend && npm run remove:infra", "export-env:infra": "cd infra && serverless export-env --stage dev --aws-profile payrix", "export-env:functions": "cd functions && serverless export-env --stage dev --aws-profile payrix", "_db:connect": "./scripts/db-connect.sh dev payrix connect", "_db:start": "./scripts/db-connect.sh dev payrix start", "_db:stop": "./scripts/db-connect.sh dev payrix stop", "_db:status": "./scripts/db-connect.sh dev payrix status", "deploy": "npm run deploy:all", "remove": "npm run remove:all", "offline": "cd functions && serverless offline start --stage dev --aws-profile payrix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.821.0", "@aws-sdk/client-ssm": "^3.536.0", "@types/aws-lambda": "^8.10.136", "@types/node": "^22.14.0", "@types/pg": "^8.11.10", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1692.0", "pg": "^8.14.1", "reflect-metadata": "^0.2.2", "serverless-export-env": "^2.2.0", "serverless-iam-roles-per-function": "^3.2.0", "serverless-offline": "^14.4.0", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typeorm": "^0.3.22", "typescript": "^5.8.2", "uuid": "^8.3.2", "yargs": "^17.7.2"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/yargs": "^17.0.32", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "esbuild": "^0.25.5", "eslint": "^9.28.0", "globals": "^16.2.0", "nodemon": "^3.1.9", "serverless-esbuild": "^1.55.0", "typescript-eslint": "^8.33.1"}}