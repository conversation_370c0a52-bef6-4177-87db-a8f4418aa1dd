Comprehensive PayFields Mode and Transaction Type Combinations Analysis
Based on the PayrixPayfields component implementation and official Payrix documentation, here's a detailed analysis of all 9 possible mode and transaction type combinations:

Transaction Mode (txn) - Payment Only
1. txn + sale 
What happens during payment processing:

Processes an immediate credit card charge
Funds are captured immediately upon successful authorization
No token is created or stored
Single-use transaction with no future payment capability
When to use this combination:

One-time purchases (e-commerce checkout)
Point-of-sale transactions
Donations or single payments
When customer data storage is not required
Expected behavior and outcomes:

API Response: Transaction object with status: "approved" for successful payments
Immediate fund capture from customer's account
No token generation
Transaction appears in merchant's settlement batch
Code reference:

src/components/Payrix
window.PayFields.config.mode = mode; // 'txn'
window.PayFields.config.txnType = txnType; // 'sale'
window.PayFields.config.amount = amount; // Full transaction amount
2. txn + auth
What happens during payment processing:

Authorizes the payment amount but does not capture funds
Holds the authorized amount on customer's card
No token creation
Requires separate capture call within authorization timeframe (typically 7-30 days)
When to use this combination:

Pre-orders where fulfillment is delayed
Hotel/rental reservations
Services requiring approval before charging
Split shipment scenarios
Expected behavior and outcomes:

API Response: Transaction with status: "authorized"
Funds held but not captured
Requires subsequent capture API call to collect payment
Authorization expires if not captured within timeframe
3. txn + ecsale
What happens during payment processing:

Processes immediate ACH/bank transfer
Funds are debited from customer's bank account
No token creation
Processing time typically 1-3 business days
When to use this combination:

Large transactions where ACH fees are preferable
B2B payments
Recurring utility bills (single payment)
Lower-cost payment processing
Expected behavior and outcomes:

API Response: Transaction object with eCheck-specific fields
Delayed fund settlement (1-3 business days)
Potential for returns/NSF up to 60 days
Lower processing fees compared to card transactions
Transaction with Token Mode (txnToken) - Payment + Token Creation
4. txnToken + sale 
What happens during payment processing:

Processes immediate credit card charge AND creates reusable token
Funds captured immediately
Customer payment method securely stored for future use
Optimal for establishing recurring payment relationships
When to use this combination:

First purchase in a subscription service
Customer onboarding with immediate payment
E-commerce with "save card for future purchases" option
Establishing customer payment profiles
Expected behavior and outcomes:

API Response: Transaction object + customer token
Immediate fund capture
Token stored for future Card-on-File transactions
Customer record created in Payrix system
Code reference:

src/components/Payrix
5. txnToken + auth
What happens during payment processing:

Authorizes payment amount without capturing AND creates token
Holds authorized amount on customer's card
Stores payment method for future use
Requires separate capture for current transaction
When to use this combination:

Pre-orders with future recurring billing
Trial periods with delayed first charge
Services requiring approval + future billing setup
Complex payment workflows requiring manual review
Expected behavior and outcomes:

API Response: Authorized transaction + customer token
Funds held but not captured
Token available for immediate future transactions
Current authorization requires capture within timeframe
6. txnToken + ecsale
What happens during payment processing:

Processes ACH/bank transfer AND creates reusable token
Bank account information securely stored
Immediate eCheck processing initiated
Token enables future ACH transactions
When to use this combination:

Subscription services preferring ACH
B2B recurring billing setup
Large transaction amounts with ongoing relationship
Cost-effective recurring payment establishment
Expected behavior and outcomes:

API Response: eCheck transaction + bank account token
Delayed fund settlement (1-3 business days)
Token enables future ACH transactions
Lower processing costs for recurring payments
Token-Only Mode ( token) - Token Creation Only
7. token + sale ⚠️ Not Recommended
What happens during payment processing:

Attempts to create token with $0 amount
Sale transaction type conflicts with $0 amount
May cause processing errors or unexpected behavior
When to use this combination:

Generally not recommended - conflicts with sale transaction nature
Expected behavior and outcomes:

Potential processing errors
Inconsistent behavior across payment processors
May be rejected by payment gateway
Code warning reference:

src/components/Payrix
8. token + auth ✅ Recommended
What happens during payment processing:

Performs $0 authorization to validate payment method
Creates reusable token without charging customer
Verifies card validity and customer information
No funds captured or held
When to use this combination:

Customer onboarding without immediate payment
Payment method verification
Setting up future recurring billing
Free trial periods with payment method validation
Expected behavior and outcomes:

API Response: $0 authorization + customer token
Payment method validated without charge
Token ready for future transactions
Customer can be charged later using stored token
Documentation reference: According to the official Payrix documentation, this is the standard pattern for "$0 Auth and Tokenization" scenarios.

9. token + ecsale ⚠️ Not Recommended
What happens during payment processing:

Attempts $0 eCheck transaction for token creation
Conflicts with eCheck sale nature (can't have $0 sale)
May cause processing errors
When to use this combination:

Generally not recommended - eCheck sales require actual amounts
Expected behavior and outcomes:

Potential processing errors
May be rejected by ACH processor
Inconsistent behavior
Best Practices and Recommendations
Recommended Combinations:
txn + sale - Standard one-time payments
txn + auth - Delayed capture scenarios
txn + ecsale - One-time ACH payments
txnToken + sale - First payment + future billing setup
txnToken + auth - Complex workflows with token creation
txnToken + ecsale - ACH with recurring capability
token + auth - Payment method validation without charge
Combinations to Avoid:
token + sale - Conflicts with $0 amount requirement
token + ecsale - eCheck sales require actual amounts
Implementation Considerations:
Error Handling Enhancement:

src/components/Payrix
Tokenization Best Practices:
Based on the tokenization documentation, always include customer address information when creating tokens to improve future transaction success rates and reduce AVS mismatches.

This comprehensive analysis provides clear guidance for selecting appropriate mode and transaction type combinations based on specific business requirements and payment scenarios.