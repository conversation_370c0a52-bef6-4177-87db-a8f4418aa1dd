# Auth Clear - Merchant Onboarding Platform

A comprehensive serverless merchant onboarding platform built with AWS Lambda, TypeORM, and React. This system provides secure merchant registration and admin management capabilities with role-based authentication.

## 🏗️ Architecture Overview

This project uses a multi-stack serverless architecture to avoid circular dependencies and ensure proper resource isolation:

### Stack Components

1. **Infrastructure** (`/infra`) - Core AWS resources including VPC, Aurora PostgreSQL, and security groups
2. **Session Manager** (`/session-manager`) - Bastion host for secure database access during development
3. **Functions** (`/functions`) - Lambda functions with API Gateway and TypeORM integration
4. **Frontend** (`/frontend`) - React SPA deployed to S3 with CloudFront distribution

### Technology Stack

- **Runtime**: Node.js 20 with TypeScript ES modules
- **Database**: Aurora PostgreSQL Serverless v2 with TypeORM
- **Authentication**: Lambda authorizer with Bearer token validation
- **Frontend**: React 19 with Vite build system
- **Infrastructure**: Serverless Framework v4
- **Security**: VPC isolation, private subnets, IAM roles

## 🚀 Quick Start

### Prerequisites

- Node.js 20+
- AWS CLI configured with `payrix` profile
- Serverless Framework v4

### Environment Setup

```bash
# Install dependencies
npm install

# Generate environment variables from AWS resources
npm run generate-env

# Deploy all stacks (infrastructure → session-manager → frontend → functions)
npm run deploy:all
```

### Local Development

```bash
# Start database tunnel for local development
npm run db:start

# Run serverless offline for API testing
npm run offline

# Frontend development (in another terminal)
cd frontend
npm run dev
```

## 📁 Project Structure

```
auth-final/
├── infra/                    # Infrastructure stack (VPC, Aurora, Security Groups)
├── session-manager/          # Database access bastion host
├── functions/               # Lambda functions and API
│   ├── src/
│   │   ├── entity/          # TypeORM entities
│   │   ├── repository/      # Data access layer
│   │   ├── service/         # Business logic
│   │   ├── functions/       # Lambda handlers
│   │   └── data-source/     # TypeORM configuration
├── frontend/                # React SPA
│   ├── src/
│   │   ├── services/        # API integration
│   │   └── components/      # React components
└── scripts/                 # Utility scripts
```

## 🛠️ Essential Commands

### AWS Profile
All commands require the AWS profile flag: `--aws-profile payrix`

### Environment & Dependencies
```bash
npm run generate-env          # Generate environment variables
npm run lint                  # Run ESLint on TypeScript files
```

### Database Management
```bash
npm run db:connect            # Connect to Aurora via bastion host
npm run db:start              # Start database tunnel
npm run db:stop               # Stop database tunnel
npm run migration:generate -- migration-name  # Generate TypeORM migration
npm run migration:run         # Run pending migrations
npm run migration:revert      # Revert last migration
```

### Deployment
```bash
npm run deploy:all            # Deploy all stacks in correct order
npm run deploy:functions      # Deploy Lambda functions only
npm run deploy:frontend       # Deploy frontend only
npm run deploy:infra          # Deploy infrastructure only
npm run remove:all            # Remove all stacks in reverse order
```

### Token & User Management
```bash
npm run token:dev:create      # Create development API token
npm run token:dev:rotate      # Rotate development token
npm run user:create-admin     # Create admin user in database
```

### Quick Function Deployment
```bash
npm run function:create-merchant   # Deploy createMerchant function
npm run function:list-merchants    # Deploy listMerchants function
```

## 🔐 Authentication & Authorization

### Public Endpoints
- `POST /merchants` - Merchant registration (no authentication required)

### Protected Endpoints
- `GET /merchants` - List merchants (requires admin token)

### Token Management
- Tokens stored in AWS Systems Manager Parameter Store
- Bearer token validation via Lambda authorizer
- Role-based access control with admin privileges

## 📊 Data Model

### Core Entities
- **Merchant** - Main entity with UUID primary key
- **MerchantMember** - Team members associated with merchants
- **MerchantBankAccount** - Banking information
- **MerchantDocument** - Document attachments
- **MerchantOnboardingStatus** - Onboarding progress tracking
- **MerchantNote** - Internal notes
- **MerchantAuditLog** - Activity audit trail
- **User** - System users with role-based access

### Database Configuration
- **Local Development**: SSH tunnel through bastion host
- **Lambda Functions**: Direct VPC access via private subnets
- **Aurora Cluster**: Isolated in private subnets with security groups

## 🌐 Frontend Configuration

### Development URLs
- Local: `http://localhost:3000`
- CloudFront: `https://d1eu8fm0ysafq4.cloudfront.net`

### CORS Configuration
Configured for CloudFront distribution and localhost development ports with Authorization header support.

### Build & Deployment
```bash
cd frontend
npm run build                 # Build production assets
npm run deploy               # Deploy to S3 and invalidate CloudFront
```

## 🔧 Development Workflow

### 1. Infrastructure Setup
```bash
npm run deploy:infra         # Deploy VPC, Aurora, security groups
npm run deploy:session-manager  # Deploy bastion host
```

### 2. Database Setup
```bash
npm run generate-env         # Get database credentials
npm run db:start            # Start tunnel
npm run migration:run       # Apply migrations
npm run user:create-admin   # Create admin user
```

### 3. Application Deployment
```bash
npm run deploy:frontend     # Deploy React app
npm run deploy:functions    # Deploy API functions
```

### 4. Local Development
```bash
npm run db:start           # Database access
npm run offline            # Local API server
cd frontend && npm run dev # Frontend dev server
```

## 🔐 Security Features

- **VPC Isolation**: All resources in private subnets
- **Security Groups**: Restrictive inbound/outbound rules
- **IAM Roles**: Least privilege access
- **Secrets Management**: Database credentials in AWS Secrets Manager
- **Token Storage**: API tokens in Systems Manager Parameter Store
- **HTTPS Only**: All traffic encrypted in transit

## 📝 Environment Variables

Generated automatically via `npm run generate-env` from AWS resources:
- Database connection details
- AWS resource ARNs
- Security group IDs
- VPC configuration

## 🧪 Testing

```bash
npm run lint                 # Code quality checks
npm run offline             # Local API testing
```

## 📋 Deployment Dependencies

**Important**: Always deploy in the correct order to avoid circular dependencies:

1. `infra` - Core infrastructure
2. `session-manager` - Database access
3. `frontend` - Static assets
4. `functions` - API and Lambda functions

Use `npm run deploy:all` to ensure proper sequencing.

## 🐛 Troubleshooting

### Database Connection Issues
```bash
npm run db:status           # Check tunnel status
npm run db:stop && npm run db:start  # Restart tunnel
npm run generate-env        # Refresh credentials
```

### Migration Issues
```bash
npm run generate-env        # Ensure proper DB connection
npm run db:connect          # Verify database access
```

### Deployment Issues
```bash
npm run remove:all          # Clean deployment
npm run deploy:all          # Fresh deployment
```

