 
# service: auth-clear-session-manager

# plugins:
#   - serverless-iam-roles-per-function

# provider:
#   name: aws
#   runtime: nodejs20.x
#   stage: ${opt:stage, 'dev'}
#   region: us-east-1

# # No functions in session-manager stack

# resources:
#   Resources:
#     # VPC Endpoints for Session Manager (required for private subnet access)
#     SSMVPCEndpoint:
#       Type: AWS::EC2::VPCEndpoint
#       Properties:
#         VpcId: ${cf:auth-clear-infra-${self:provider.stage}.VpcId}
#         ServiceName: com.amazonaws.${self:provider.region}.ssm
#         VpcEndpointType: Interface
#         SubnetIds:
#           - ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetA}
#           - ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetB}
#         SecurityGroupIds:
#           - !Ref VPCEndpointSecurityGroup
#         PolicyDocument:
#           Version: '2012-10-17'
#           Statement:
#             - Effect: Allow
#               Principal: '*'
#               Action:
#                 - ssm:*
#               Resource: '*'

#     SSMMessagesVPCEndpoint:
#       Type: AWS::EC2::VPCEndpoint
#       Properties:
#         VpcId: ${cf:auth-clear-infra-${self:provider.stage}.VpcId}
#         ServiceName: com.amazonaws.${self:provider.region}.ssmmessages
#         VpcEndpointType: Interface
#         SubnetIds:
#           - ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetA}
#           - ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetB}
#         SecurityGroupIds:
#           - !Ref VPCEndpointSecurityGroup

#     EC2MessagesVPCEndpoint:
#       Type: AWS::EC2::VPCEndpoint
#       Properties:
#         VpcId: ${cf:auth-clear-infra-${self:provider.stage}.VpcId}
#         ServiceName: com.amazonaws.${self:provider.region}.ec2messages
#         VpcEndpointType: Interface
#         SubnetIds:
#           - ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetA}
#           - ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetB}
#         SecurityGroupIds:
#           - !Ref VPCEndpointSecurityGroup

#     # Security Group for VPC Endpoints
#     VPCEndpointSecurityGroup:
#       Type: AWS::EC2::SecurityGroup
#       Properties:
#         GroupDescription: Security group for VPC endpoints
#         VpcId: ${cf:auth-clear-infra-${self:provider.stage}.VpcId}
#         SecurityGroupIngress:
#           - IpProtocol: tcp
#             FromPort: 443
#             ToPort: 443
#             SourceSecurityGroupId: !Ref SessionManagerSecurityGroup
#         Tags:
#           - Key: Name
#             Value: ${self:service}-${self:provider.stage}-vpc-endpoint-sg

#     # Session Manager Instance Security Group
#     SessionManagerSecurityGroup:
#       Type: AWS::EC2::SecurityGroup
#       Properties:
#         GroupDescription: Security group for Session Manager database access instance
#         VpcId: ${cf:auth-clear-infra-${self:provider.stage}.VpcId}
#         SecurityGroupEgress:
#           - IpProtocol: tcp
#             FromPort: 443
#             ToPort: 443
#             CidrIp: 0.0.0.0/0
#             Description: HTTPS for Session Manager
#           - IpProtocol: tcp
#             FromPort: 5432
#             ToPort: 5432
#             DestinationSecurityGroupId: ${cf:auth-clear-infra-${self:provider.stage}.AuroraSecurityGroupId}
#             Description: PostgreSQL to Aurora
#         Tags:
#           - Key: Name
#             Value: ${self:service}-${self:provider.stage}-session-manager-sg

#     # IAM Role for Session Manager Instance
#     SessionManagerInstanceRole:
#       Type: AWS::IAM::Role
#       Properties:
#         AssumeRolePolicyDocument:
#           Version: '2012-10-17'
#           Statement:
#             - Effect: Allow
#               Principal:
#                 Service: ec2.amazonaws.com
#               Action: sts:AssumeRole
#         ManagedPolicyArns:
#           - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
#         Tags:
#           - Key: Name
#             Value: ${self:service}-${self:provider.stage}-session-manager-role

#     # Instance Profile for Session Manager Instance
#     SessionManagerInstanceProfile:
#       Type: AWS::IAM::InstanceProfile
#       Properties:
#         Roles:
#           - !Ref SessionManagerInstanceRole

#     # Session Manager Instance (for database access)
#     SessionManagerInstance:
#       Type: AWS::EC2::Instance
#       Properties:
#         InstanceType: t3.micro
#         SubnetId: ${cf:auth-clear-infra-${self:provider.stage}.PrivateSubnetA}
#         ImageId: ami-0c02fb55956c7d316  # Amazon Linux 2 AMI (us-east-1)
#         IamInstanceProfile: !Ref SessionManagerInstanceProfile
#         SecurityGroupIds:
#           - !Ref SessionManagerSecurityGroup
#         UserData:
#           Fn::Base64: !Sub |
#             #!/bin/bash
#             yum update -y
#             yum install -y postgresql15
#         Tags:
#           - Key: Name
#             Value: ${self:service}-${self:provider.stage}-session-manager
#           - Key: Purpose
#             Value: Database Access via Session Manager
    
#     # Update Aurora security group to allow access from Session Manager instance
#     AuroraIngressRule:
#       Type: AWS::EC2::SecurityGroupIngress
#       Properties:
#         Description: Allow PostgreSQL access from Session Manager instance
#         GroupId: ${cf:auth-clear-infra-${self:provider.stage}.AuroraSecurityGroupId}
#         IpProtocol: tcp
#         FromPort: 5432
#         ToPort: 5432
#         SourceSecurityGroupId: !Ref SessionManagerSecurityGroup
            
#   Outputs:
#     SessionManagerInstanceId:
#       Value: !Ref SessionManagerInstance
#       Export:
#         Name: ${self:service}-${self:provider.stage}-session-manager-instance-id
        
#     SessionManagerSecurityGroupId:
#       Value: !Ref SessionManagerSecurityGroup
#       Export:
#         Name: ${self:service}-${self:provider.stage}-session-manager-sg-id

#     # Connection instructions
#     ConnectionInstructions:
#       Value: !Sub |
#         To connect to the database locally:
#         1. Start the instance: aws ec2 start-instances --instance-ids ${SessionManagerInstance} --profile payrix
#         2. Wait for running state: aws ec2 wait instance-running --instance-ids ${SessionManagerInstance} --profile payrix
#         3. Start port forwarding: aws ssm start-session --target ${SessionManagerInstance} --document-name AWS-StartPortForwardingSessionToRemoteHost --parameters host="${cf:auth-clear-infra-${self:provider.stage}.AuroraClusterEndpoint}",portNumber="5432",localPortNumber="5432" --profile payrix
#         4. Connect locally to: localhost:5432
#         5. Stop instance when done: aws ec2 stop-instances --instance-ids ${SessionManagerInstance} --profile payrix
#       Export:
#         Name: ${self:service}-${self:provider.stage}-connection-instructions 