# React Router DOM Setup

This frontend application uses React Router DOM v7 for client-side routing with Redux integration.

## Route Structure

```
/                    → Home page (public)
/login               → Login form (redirects if authenticated)
/onboarding          → Multi-step onboarding process (protected)
/onboarding-success  → Onboarding completion page (public)
/payment             → Payment processing page (protected)
/auth/*              → Authentication dashboard routes (protected)
  ├── /auth          → Auth dashboard
  └── /auth/settings → Auth settings
/error/*             → Error pages
  ├── /error/404     → Page not found
  ├── /error/403     → Access forbidden
  └── /error/500     → Server error
/*                   → Catch-all redirects to 404
```

## Features

### 🔐 Protected Routes
- Routes that require authentication automatically redirect to `/login`
- Authentication state is managed by Redux with persistence
- Seamless navigation after login

### 🔄 State-Aware Navigation
- Navigation component shows different options based on auth state
- Active route highlighting
- User context display

### 🚫 Error Handling
- Dedicated error routes with user-friendly messages
- 404 handling for invalid URLs
- Graceful error recovery

### 📱 Responsive Design
- All routes are mobile-friendly
- Consistent styling across pages
- Modern UI components

## Component Architecture

### Pages (`/src/pages/`)
- `Home.tsx` - Landing page with auth demo
- `Login.tsx` - Authentication form with redirect logic
- `Onboarding.tsx` - Multi-step business onboarding
- `OnboardingSuccess.tsx` - Success confirmation page
- `PaymentPage.tsx` - Payment processing interface

### Routing Components (`/src/components/routing/`)
- `ErrorsRouting.tsx` - Error page routing and display
- `AuthPage.tsx` - Protected auth dashboard routes

### Navigation (`/src/components/`)
- `Navigation.tsx` - Main navigation bar with auth state
- `AuthDemo.tsx` - Redux state demonstration component

## Usage Examples

### Basic Navigation
```tsx
import { Link } from 'react-router-dom';

// Simple navigation
<Link to="/onboarding">Start Onboarding</Link>

// Programmatic navigation
import { useNavigate } from 'react-router-dom';
const navigate = useNavigate();
navigate('/payment');
```

### Protected Route Pattern
```tsx
import { useAppSelector } from '../redux';
import { Navigate } from 'react-router-dom';

export const ProtectedPage = () => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <div>Protected content</div>;
};
```

### Auth-Aware Components
```tsx
import { useAppSelector } from '../redux';

export const ConditionalComponent = () => {
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);
  
  return (
    <div>
      {isAuthenticated ? (
        <p>Welcome {user?.name}!</p>
      ) : (
        <Link to="/login">Please login</Link>
      )}
    </div>
  );
};
```

## Route Guards

### Authentication Check
```tsx
// In protected components
const { isAuthenticated } = useAppSelector((state) => state.auth);

if (!isAuthenticated) {
  return <Navigate to="/login" replace />;
}
```

### Role-Based Access (Future Enhancement)
```tsx
// Example for role-based protection
const { user } = useAppSelector((state) => state.auth);

if (!user?.roles?.includes('admin')) {
  return <Navigate to="/error/403" replace />;
}
```

## State Management Integration

### Redux Persistence
- Authentication state persists across browser sessions
- Automatic rehydration on app load
- Seamless login state maintenance

### Navigation State
- Current route tracking
- Active link highlighting
- User context awareness

## Development Tips

### Adding New Routes
1. Create page component in `/src/pages/`
2. Add route to `App.tsx`
3. Update navigation if needed
4. Add protection logic if required

### Error Handling
- Use `/error/404` for not found
- Use `/error/403` for access denied
- Use `/error/500` for server errors
- Custom error messages in `ErrorsRouting.tsx`

### Testing Routes
```bash
# Start development server
npm run dev

# Test all routes
http://localhost:5173/
http://localhost:5173/login
http://localhost:5173/onboarding
http://localhost:5173/payment
http://localhost:5173/auth
http://localhost:5173/error/404
```

## Best Practices

1. **Lazy Loading** - Consider code splitting for large pages
2. **SEO** - Add meta tags and titles for each route
3. **Analytics** - Track route changes for user behavior
4. **Loading States** - Show loading indicators during navigation
5. **Breadcrumbs** - Add breadcrumb navigation for complex flows

## Security Considerations

- All sensitive routes require authentication
- Token validation on protected pages
- Automatic logout on token expiration
- CSRF protection for forms
- Input validation on all forms

## Performance Optimizations

- Route-based code splitting
- Preloading critical routes
- Lazy loading of heavy components
- Optimized Redux selectors
- Memoized route components 