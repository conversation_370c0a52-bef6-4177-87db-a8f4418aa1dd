 
service: auth-clear-frontend

provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'dev'}
  region: us-east-1

# No functions in frontend infrastructure stack

resources:
  Resources:
    # S3 bucket for hosting the frontend
    FrontendBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:service}-${self:provider.stage}
        AccessControl: Private
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}
          - Key: Environment
            Value: ${self:provider.stage}

    # CloudFront Origin Access Control
    CloudFrontOriginAccessControl:
      Type: AWS::CloudFront::OriginAccessControl
      Properties:
        OriginAccessControlConfig:
          Name: ${self:service}-${self:provider.stage}-s3-oac
          OriginAccessControlOriginType: s3
          SigningBehavior: always
          SigningProtocol: sigv4

    # CloudFront distribution
    CloudFrontDistribution:
      Type: AWS::CloudFront::Distribution
      Properties:
        DistributionConfig:
          Enabled: true
          DefaultRootObject: index.html
          HttpVersion: http2
          PriceClass: PriceClass_100
          # Custom error responses for SPA routing
          CustomErrorResponses:
            - ErrorCode: 403
              ResponseCode: 200
              ResponsePagePath: /index.html
            - ErrorCode: 404
              ResponseCode: 200
              ResponsePagePath: /index.html
          # S3 Origin
          Origins:
            - Id: S3Origin
              DomainName: ${self:service}-${self:provider.stage}.s3.amazonaws.com
              S3OriginConfig:
                OriginAccessIdentity: ''
              OriginAccessControlId: !GetAtt CloudFrontOriginAccessControl.Id
          DefaultCacheBehavior:
            TargetOriginId: S3Origin
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 658327ea-f89d-4fab-a63d-7e88639e58f6 # CachingOptimized policy
            OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf # CORS-S3Origin policy
            Compress: true
            FunctionAssociations: []
          # Allow secure access to the API from CloudFront
          # Origins:
          #   - Id: APIOrigin
          #     DomainName: ${cf:auth-clear-functions-${self:provider.stage}.ApiDomainName}
          #     CustomOriginConfig:
          #       OriginProtocolPolicy: https-only
          #       OriginSSLProtocols:
          #         - TLSv1.2
          # CacheBehaviors:
          #   - PathPattern: /api/*
          #     TargetOriginId: APIOrigin
          #     ViewerProtocolPolicy: https-only
          #     AllowedMethods:
          #       - DELETE
          #       - GET
          #       - HEAD
          #       - OPTIONS
          #       - PATCH
          #       - POST
          #       - PUT
          #     CachedMethods:
          #       - GET
          #       - HEAD
          #       - OPTIONS
          #     CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad # CachingDisabled policy
          #     OriginRequestPolicyId: 216adef6-5c7f-47e4-b989-5492eafa07d3 # AllViewer policy
          # Tags:
          #   - Key: Name
          #     Value: ${self:service}-${self:provider.stage}
          #   - Key: Environment
          #     Value: ${self:provider.stage}

    # Bucket policy to allow CloudFront access
    FrontendBucketPolicy:
      Type: AWS::S3::BucketPolicy
      Properties:
        Bucket: !Ref FrontendBucket
        PolicyDocument:
          Statement:
            - Effect: Allow
              Principal:
                Service: cloudfront.amazonaws.com
              Action: s3:GetObject
              Resource: !Join ['', ['arn:aws:s3:::', !Ref FrontendBucket, '/*']]
              Condition:
                StringEquals:
                  AWS:SourceArn: !Join ['', ['arn:aws:cloudfront::', !Ref AWS::AccountId, ':distribution/', !Ref CloudFrontDistribution]]

  Outputs:
    FrontendBucketName:
      Value: !Ref FrontendBucket
      Export:
        Name: ${self:service}-${self:provider.stage}-bucket-name

    CloudFrontDistributionId:
      Value: !Ref CloudFrontDistribution
      Export:
        Name: ${self:service}-${self:provider.stage}-distribution-id

    CloudFrontDomainName:
      Value: !GetAtt CloudFrontDistribution.DomainName
      Export:
        Name: ${self:service}-${self:provider.stage}-domain-name 