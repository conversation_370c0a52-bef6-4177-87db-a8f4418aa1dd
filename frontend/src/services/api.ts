import axios, { type AxiosResponse, type AxiosError } from "axios";

const API_URL = import.meta.env.VITE_API_URL;

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  userType: string;
}

interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    token: string;
  };
  error?: string;
}

interface CreateAdminRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  inviteCode: string;
}

interface CreateAdminResponse {
  success: boolean;
  message: string;
  data?: {
    userId: string;
    email: string;
    name: string;
    token: string;
  };
  error?: string;
}

// Basic merchant creation interface (current implementation)
interface CreateMerchantRequest {
  businessName: string;
  legalName: string;
  email: string;
  phone: string;
  website?: string;
}

// Enhanced merchant creation interface (Payrix format)
interface CreatePayrixMerchantRequest {
  // Business Information
  name: string; // Legal Business Name
  dba?: string; // Statement Descriptor/Doing Business As
  ein: string; // Tax ID Number
  type: number; // Business type (2 for merchant)
  public: number; // 0 = Private, 1 = Public
  website: string;
  email: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone: string;
  customerPhone?: string;
  fax?: string;
  mcc: string; // 4-digit Merchant Category Code
  status: number; // 1 = ready, 5 = incomplete
  tcVersion: string;
  tcDate: string;
  clientIp: string;
  currency: string;

  // Compliance fields
  tcAttestation?: number;
  visaDisclosure?: number;
  disclosureIP?: string;
  disclosureDate?: string;
  merchantIp?: string;

  // User account creation fields
  username?: string;
  password?: string;
  confirmPassword?: string; // Not sent to API, used for validation only
  createAccount?: boolean;

  // Payment Parameters
  annualCCSales?: number;
  avgTicket?: number;
  established?: string;

  // Bank Account Information
  accounts: {
    primary: number; // 1 = primary
    currency: string;
    account: {
      method: number; // 8-11 for account types
      number: string;
      routing: string;
    };
  }[];

  // Primary Owner Information
  merchant: {
    dba?: string;
    new: number; // 1 = new merchant
    mcc: string;
    status: string;
    annualCCSales?: number;
    avgTicket?: number;
    established?: string;
    members: {
      title: string;
      first: string;
      middle?: string;
      last: string;
      ssn?: string;
      dob: string; // YYYYMMDD
      dl?: string;
      dlstate?: string;
      ownership: number; // In basis points (10000 = 100%)
      significantResponsibility: number; // 1 = yes
      politicallyExposed: number; // 0 = no
      email: string;
      phone: string;
      fax?: string;
      primary: string; // '1' = primary
      address1: string;
      address2?: string;
      city: string;
      state: string;
      zip: string;
      country: string;
    }[];
  };
}

interface MerchantResponse {
  success: boolean;
  message: string;
  merchantId: string;
  payrixEntityId?: string;
  data?: {
    merchant: {
      merchant_id: string;
      legal_name: string;
      email: string;
      verification_status: number;
    };
    payrixResponse?: unknown;
  };
  error?: string;
  errors?: string[];
}

// New Payment Config Interfaces
interface PaymentConfigRequest {
  merchantId: string;
  description: string;
  amount?: number;
}

interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  returnUrl?: string;
}

interface PaymentConfigResponse {
  config: PayFieldsConfig;
  message: string;
  merchantInfo: {
    id: string;
    name: string;
    status: number;
  };
}

// ===================================================================
// API FUNCTIONS TEMPORARILY DISABLED
// ===================================================================
// All database-dependent API functions have been commented out
// because the database backend is temporarily disabled.
//
// To re-enable: Uncomment the functions below
// ===================================================================

/*
// API functions
export const login = async (credentials: LoginRequest): Promise<LoginResponse> => {
  try {
    const response: AxiosResponse<LoginResponse> = await axios.post(`${API_URL}/auth/login`, credentials);
    return response.data;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.data) {
      return axiosError.response.data as LoginResponse;
    }
    return {
      success: false,
      message: "Network error occurred",
      error: axiosError.message,
    };
  }
};
*/

/*
export const createAdmin = async (adminData: CreateAdminRequest): Promise<CreateAdminResponse> => {
  try {
    const response: AxiosResponse<CreateAdminResponse> = await axios.post(`${API_URL}/admin/register`, adminData);
    return response.data;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.data) {
      return axiosError.response.data as CreateAdminResponse;
    }
    return {
      success: false,
      message: "Network error occurred",
      error: axiosError.message,
    };
  }
};
*/

/*
export const getMerchants = async () => {
  try {
    const response = await axios.get(`${API_URL}/merchants`);
    return response.data;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    throw new Error((axiosError.response?.data as { message?: string })?.message || "Failed to fetch merchants");
  }
};
*/

export const createMerchant = async (merchantData: CreatePayrixMerchantRequest): Promise<MerchantResponse> => {
  try {
    const response: AxiosResponse<MerchantResponse> = await axios.post(`${API_URL}/merchants/onboard`, merchantData);
    return response.data;
  } catch (error) {
    console.error("Error creating merchant:", error);
    throw error;
  }
};

// Payment Token API Functions

// New consolidated payment config API function
export const generatePaymentConfig = async (configData: PaymentConfigRequest): Promise<PaymentConfigResponse> => {
  try {
    const response: AxiosResponse<PaymentConfigResponse> = await axios.post(`${API_URL}/payments/generate-payment-config`, configData);
    return response.data;
  } catch (error) {
    console.error("Error generating payment config:", error);
    throw error;
  }
};

// Iframe Integration API Functions

// Generate integration token for iframe embedding
export const generateIntegrationToken = async (tokenData: {
  merchantId: string;
  description: string;
  amount?: number;
  returnUrl?: string;
  expiresIn?: number;
}): Promise<{
  success: boolean;
  message: string;
  data: {
    token: string;
    expiresAt: string;
    embedUrl: string;
    merchantInfo: {
      id: string;
      name: string;
      status: number;
    };
  };
}> => {
  try {
    const response: AxiosResponse<{
      success: boolean;
      message: string;
      data: {
        token: string;
        expiresAt: string;
        embedUrl: string;
        merchantInfo: {
          id: string;
          name: string;
          status: number;
        };
      };
    }> = await axios.post(`${API_URL}/payments/generate-integration-token`, tokenData);
    return response.data;
  } catch (error) {
    console.error("Error generating integration token:", error);
    throw error;
  }
};

// Validate iframe token
export const validateIframeToken = async (
  token: string
): Promise<{
  success: boolean;
  message: string;
  data?: {
    config: PayFieldsConfig;
    merchantInfo: {
      id: string;
      name: string;
      status: number;
    };
    paymentInfo: {
      description: string;
      amount: number;
      returnUrl?: string;
    };
  };
}> => {
  try {
    const response: AxiosResponse<{
      success: boolean;
      message: string;
      data?: {
        config: PayFieldsConfig;
        merchantInfo: {
          id: string;
          name: string;
          status: number;
        };
        paymentInfo: {
          description: string;
          amount: number;
          returnUrl?: string;
        };
      };
    }> = await axios.post(`${API_URL}/payments/validate-iframe-token`, { token });
    return response.data;
  } catch (error) {
    console.error("Error validating iframe token:", error);
    throw error;
  }
};

// Get iframe configuration
export const getIframeConfig = async (params?: {
  domain?: string;
  theme?: "light" | "dark" | "auto";
  language?: string;
  currency?: string;
}): Promise<{
  success: boolean;
  message: string;
  data: {
    payrixConfig: {
      scriptUrl: string;
      environment: string;
      supportedFeatures: string[];
    };
    styling: {
      theme: string;
      customCSS: Record<string, string>;
    };
    security: {
      allowedOrigins: string[];
      cspDirectives: string[];
    };
    features: {
      autoResize: boolean;
      responsiveDesign: boolean;
      mobileOptimized: boolean;
    };
  };
}> => {
  try {
    const response: AxiosResponse<{
      success: boolean;
      message: string;
      data: {
        payrixConfig: {
          scriptUrl: string;
          environment: string;
          supportedFeatures: string[];
        };
        styling: {
          theme: string;
          customCSS: Record<string, string>;
        };
        security: {
          allowedOrigins: string[];
          cspDirectives: string[];
        };
        features: {
          autoResize: boolean;
          responsiveDesign: boolean;
          mobileOptimized: boolean;
        };
      };
    }> = await axios.get(`${API_URL}/payments/iframe-config`, { params });
    return response.data;
  } catch (error) {
    console.error("Error getting iframe configuration:", error);
    throw error;
  }
};

// Check token status
export const checkTokenStatus = async (
  token: string
): Promise<{
  success: boolean;
  message: string;
  data: {
    isValid: boolean;
    status: "valid" | "expired" | "used" | "invalid";
    expiresAt?: string;
    timeRemaining?: number;
    merchantId?: string;
    amount?: number;
    description?: string;
  };
}> => {
  try {
    const response: AxiosResponse<{
      success: boolean;
      message: string;
      data: {
        isValid: boolean;
        status: "valid" | "expired" | "used" | "invalid";
        expiresAt?: string;
        timeRemaining?: number;
        merchantId?: string;
        amount?: number;
        description?: string;
      };
    }> = await axios.get(`${API_URL}/payments/token-status`, {
      params: { token },
    });
    return response.data;
  } catch (error) {
    console.error("Error checking token status:", error);
    throw error;
  }
};

// Token Payment Processing
interface TokenPaymentRequest {
  merchantId: string;
  token: string;
  amount: number;
  description?: string;
  customerInfo?: {
    email?: string;
    name?: string;
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
  };
}

interface TokenPaymentResponse {
  success: boolean;
  message: string;
  transaction?: {
    id: string;
    status: string;
    amount: number;
    merchantId: string;
    description: string;
    createdAt: string;
  };
  merchantInfo?: {
    id: string;
    name: string;
    status: number;
  };
  error?: string;
}

export const processTokenPayment = async (paymentData: TokenPaymentRequest): Promise<TokenPaymentResponse> => {
  try {
    console.log("Processing token payment:", {
      merchantId: paymentData.merchantId,
      token: paymentData.token.substring(0, 8) + "...",
      amount: paymentData.amount,
      description: paymentData.description,
    });

    const response: AxiosResponse<TokenPaymentResponse> = await axios.post(`${API_URL}/payments/process-token-payment`, paymentData);

    console.log("Token payment processed successfully:", {
      transactionId: response.data.transaction?.id,
      status: response.data.transaction?.status,
      amount: response.data.transaction?.amount,
    });

    return response.data;
  } catch (error) {
    console.error("Error processing token payment:", error);
    const axiosError = error as AxiosError;
    if (axiosError.response?.data) {
      return axiosError.response.data as TokenPaymentResponse;
    }
    return {
      success: false,
      message: "Network error occurred during token payment processing",
      error: axiosError.message,
    };
  }
};

// Export types
export type {
  User,
  LoginRequest,
  LoginResponse,
  CreateAdminRequest,
  CreateAdminResponse,
  CreateMerchantRequest,
  CreatePayrixMerchantRequest,
  MerchantResponse,
  PaymentConfigRequest,
  PaymentConfigResponse,
  PayFieldsConfig,
  TokenPaymentRequest,
  TokenPaymentResponse,
};
