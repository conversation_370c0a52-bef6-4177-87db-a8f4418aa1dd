import { useState, useRef, useEffect } from "react";
import { toast } from "sonner";
import { generateIntegrationToken } from "../services/api";
import { PageLayout } from "../components/PageLayout";
import { ContentCard } from "../components/ContentCard";

interface DemoConfig {
  merchantId: string;
  description: string;
  amount: number;
  returnUrl: string;
}

interface PaymentEvent {
  type: string;
  data: string | Record<string, unknown> | null;
  timestamp: string;
}

const IframeDemoPage = () => {
  const [config, setConfig] = useState<DemoConfig>({
    merchantId: "t1_mer_6859ab2363c6d277d46676d",
    description: "Demo Product Purchase",
    amount: 2500, // $25.00
    returnUrl: "https://example.com/success",
  });

  const [embedUrl, setEmbedUrl] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [events, setEvents] = useState<PaymentEvent[]>([]);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Listen for iframe messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // In production, verify the origin
      // if (event.origin !== 'https://your-payment-domain.com') return;

      const eventData = {
        type: event.data.type,
        data: event.data.data || event.data.error || event.data,
        timestamp: new Date().toISOString(),
      };

      setEvents((prev) => [eventData, ...prev.slice(0, 9)]); // Keep last 10 events

      switch (event.data.type) {
        case "PAYMENT_IFRAME_READY":
          setIframeLoaded(true);
          toast.success("Payment iframe loaded successfully!");
          break;
        case "PAYMENT_SUCCESS":
          toast.success("Payment successful!");
          break;
        case "PAYMENT_FAILURE":
          toast.error(`Payment failed: ${event.data.error}`);
          break;
        case "PAYMENT_VALIDATION_FAILURE":
          toast.error(`Validation failed: ${event.data.error}`);
          break;
        case "PAYMENT_TIMEOUT":
          toast.error("Payment timed out");
          break;
      }
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  const generateToken = async () => {
    setLoading(true);
    setEmbedUrl("");
    setIframeLoaded(false);
    setEvents([]);

    try {
      const response = await generateIntegrationToken({
        merchantId: config.merchantId,
        description: config.description,
        amount: config.amount,
        returnUrl: config.returnUrl,
        expiresIn: 60, // 1 hour
      });

      if (response.success && response.data) {
        setEmbedUrl(response.data.embedUrl);
        toast.success("Integration token generated successfully!");
      } else {
        throw new Error("Failed to generate token");
      }
    } catch (error) {
      console.error("Error generating token:", error);
      toast.error("Failed to generate integration token");
    } finally {
      setLoading(false);
    }
  };

  const clearEvents = () => {
    setEvents([]);
  };

  // Define sections for sidebar navigation
  const sections = [
    { id: "configuration", title: "Payment Configuration" },
    { id: "iframe-demo", title: "Payment Iframe" },
    { id: "event-log", title: "Event Log" },
    // { id: "test-instructions", title: "Testing Instructions" },
    { id: "integration-url", title: "Integration URL" },
  ];

  return (
    <PageLayout
      title="Live Iframe Integration Demo"
      subtitle="Test the complete iframe payment integration flow in real-time"
      sections={sections}
      gradientFrom="from-slate-800"
      gradientTo="to-slate-900"
    >
      {/* Configuration Section */}
      <ContentCard id="configuration" title="Payment Configuration" variant="highlight">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Merchant ID</label>
              <input
                type="text"
                value={config.merchantId}
                onChange={(e) => setConfig((prev) => ({ ...prev, merchantId: e.target.value }))}
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
                placeholder="Enter merchant ID"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Description</label>
              <input
                type="text"
                value={config.description}
                onChange={(e) => setConfig((prev) => ({ ...prev, description: e.target.value }))}
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
                placeholder="Payment description"
              />
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Amount (cents)</label>
              <input
                type="number"
                value={config.amount}
                onChange={(e) => setConfig((prev) => ({ ...prev, amount: parseInt(e.target.value) || 0 }))}
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
                placeholder="Amount in cents"
                min="1"
              />
              <p className="text-sm text-slate-500 mt-1">${(config.amount / 100).toFixed(2)}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Return URL</label>
              <input
                type="url"
                value={config.returnUrl}
                onChange={(e) => setConfig((prev) => ({ ...prev, returnUrl: e.target.value }))}
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
                placeholder="https://yoursite.com/success"
              />
            </div>
          </div>

          <div className="md:col-span-2 lg:col-span-1 flex items-end">
            <button
              onClick={generateToken}
              disabled={loading || !config.merchantId || !config.description}
              className="w-full bg-gradient-to-r from-slate-800 to-slate-900 text-white py-3 px-6 rounded-lg hover:from-slate-900 hover:to-black disabled:from-slate-400 disabled:to-slate-500 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
            >
              {loading ? "Generating Token..." : "Generate Integration Token"}
            </button>
          </div>
        </div>
      </ContentCard>

      {/* Iframe Demo Section */}
      <ContentCard id="iframe-demo" title="Payment Iframe">
        {!embedUrl ? (
          <div className="bg-gradient-to-br from-slate-50 to-gray-50 border-2 border-dashed border-slate-300 rounded-xl p-12 text-center">
            <div className="text-slate-500">
              <svg className="w-16 h-16 mx-auto mb-6 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <p className="text-xl font-semibold text-slate-700 mb-2">Generate a token to load the payment iframe</p>
              <p className="text-slate-600">Configure your payment details and click &quot;Generate Integration Token&quot;</p>
            </div>
          </div>
        ) : (
          <div className="border border-slate-300 rounded-xl overflow-hidden shadow-lg">
            <div className="bg-gradient-to-r from-slate-50 to-gray-50 px-6 py-3 border-b border-slate-200 flex items-center justify-between">
              <span className="text-sm font-medium text-slate-700">Payment Iframe</span>
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${iframeLoaded ? "bg-slate-600" : "bg-slate-400"} shadow-sm`}></div>
                <span className="text-xs text-slate-600 font-medium">{iframeLoaded ? "Loaded" : "Loading..."}</span>
              </div>
            </div>
            <div className="aspect-video lg:aspect-[16/10]">
              <iframe 
                ref={iframeRef} 
                src={embedUrl} 
                className="w-full h-full border-none" 
                allow="payment" 
                title="Payment Iframe Demo" 
              />
            </div>
          </div>
        )}
      </ContentCard>

      {/* Event Log Section */}
      <ContentCard id="event-log" title="Event Log">
        <div className="flex justify-between items-center mb-4">
          <p className="text-slate-600">Real-time events from the payment iframe</p>
          <button
            onClick={clearEvents}
            className="text-sm text-slate-500 hover:text-slate-700 px-3 py-1 rounded-md hover:bg-slate-100 transition-colors"
          >
            Clear Events
          </button>
        </div>
        <div className="bg-slate-900 text-slate-300 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm shadow-inner">
          {events.length === 0 ? (
            <div className="text-slate-500 text-center py-8">
              <svg className="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              No events yet. Generate a token and interact with the iframe to see events.
            </div>
          ) : (
            events.map((event, index) => (
              <div key={index} className="mb-2 border-l-2 border-slate-500/30 pl-3">
                <span className="text-slate-400">[{new Date(event.timestamp).toLocaleTimeString()}]</span>
                <span className="text-slate-200 ml-2 font-semibold">{event.type}</span>
                {event.data && (
                  <div className="text-slate-300 ml-4 text-xs mt-1 bg-slate-800/50 p-2 rounded">
                    {typeof event.data === "string" ? event.data : JSON.stringify(event.data, null, 2)}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </ContentCard>

      {/* Integration URL Section */}
      {embedUrl && (
        <ContentCard id="integration-url" title="Integration URL" variant="highlight">
          <div className="flex justify-between items-center mb-4">
            <p className="text-slate-600">Use this URL to embed the payment iframe in your application:</p>
            <button
              onClick={() => {
                navigator.clipboard.writeText(embedUrl);
                toast.success("URL copied to clipboard!");
              }}
              className="px-4 py-2 bg-slate-800 text-white rounded-lg hover:bg-slate-900 transition-colors text-sm font-medium shadow-sm hover:shadow-md"
            >
              Copy URL
            </button>
          </div>
          <code className="text-sm text-slate-700 break-all bg-white p-4 rounded-lg border block font-mono shadow-inner">{embedUrl}</code>
        </ContentCard>
      )}

      {/* Test Instructions */}
      {/* <ContentCard id="test-instructions" title="Testing Instructions" variant="success">
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                />
              </svg>
              Test Card Numbers
            </h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border">
                <span className="text-sm font-medium">Visa (Success):</span>
                <code className="text-slate-700 font-mono text-sm">4111 1111 1111 1111</code>
              </div>
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border">
                <span className="text-sm font-medium">Visa (Decline):</span>
                <code className="text-red-600 font-mono text-sm">4000 0000 0000 0002</code>
              </div>
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border">
                <span className="text-sm font-medium">Mastercard:</span>
                <code className="text-slate-700 font-mono text-sm">5555 5555 5555 4444</code>
              </div>
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border">
                <span className="text-sm font-medium">Expiry:</span>
                <code className="text-slate-600 font-mono text-sm">12/25</code>
              </div>
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border">
                <span className="text-sm font-medium">CVV:</span>
                <code className="text-slate-600 font-mono text-sm">123</code>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              What to Watch
            </h4>
            <ul className="space-y-2">
              {[
                "Event log shows real-time iframe communication",
                "Try different card numbers to see success/failure flows",
                "Notice how the iframe handles validation errors",
                "Observe the responsive design within the iframe",
                "Check browser console for additional debug info",
              ].map((item, index) => (
                <li key={index} className="flex items-start p-3 bg-white rounded-lg border">
                  <svg className="w-4 h-4 text-slate-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-sm text-slate-700">{item}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </ContentCard> */}
    </PageLayout>
  );
};

export default IframeDemoPage;
