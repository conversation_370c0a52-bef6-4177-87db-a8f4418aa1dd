// ===================================================================
// LOGIN PAGE TEMPORARILY DISABLED
// ===================================================================
// This entire login page has been commented out because the database
// backend is temporarily disabled. All authentication functionality
// is not available.
//
// To re-enable: Uncomment all the code below
// ===================================================================

/*
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "../redux";
import { signIn } from "../redux";
import { login } from "../services/api";
import AdminRegistration from "../components/AdminRegistration";

const Login = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showAdminRegistration, setShowAdminRegistration] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await login({ email, password });

      if (response.success && response.data) {
        dispatch(
          signIn({
            user: {
              id: response.data.user.id,
              email: response.data.user.email,
              name: response.data.user.name,
              role: response.data.user.role,
              userType: response.data.user.userType,
            },
            token: response.data.token,
          })
        );

        navigate("/");
      } else {
        setError(response.message || "Login failed");
      }
    } catch (error) {
      console.error(error);
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAdminRegistrationSuccess = () => {
    navigate("/");
  };

  if (showAdminRegistration) {
    return (
      <div className="min-h-screen flex items-center justify-center px-6 bg-slate-50">
        <div className="w-full max-w-md">
          <AdminRegistration onBack={() => setShowAdminRegistration(false)} onSuccess={handleAdminRegistrationSuccess} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-6 bg-slate-50">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <img src="/LogoFiles/svg/Black logo - no background.svg" alt="Auth-Clear" className="h-12 w-auto mx-auto mb-6" />
          <h1 className="text-3xl font-semibold text-slate-800 mb-2">Welcome Back</h1>
          <p className="text-slate-600">Sign in to access your fintech platform</p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-900 focus:border-transparent transition-colors"
              placeholder="Enter your email"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-slate-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-900 focus:border-transparent transition-colors"
              placeholder="Enter your password"
            />
          </div>

          <button
            type="submit"
            disabled={isLoading || !email || !password}
            className="w-full py-3 bg-slate-900 text-white rounded-lg hover:bg-slate-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? "Signing In..." : "Sign In"}
          </button>
        </form>

        <div className="mt-6 text-center">
          <button type="button" onClick={() => setShowAdminRegistration(true)} className="text-sm text-slate-600 hover:text-slate-800 underline">
            Create Platform Admin Account
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;
*/

// Mock Login component for when authentication is disabled
const Login = () => {
  return (
    <div className="min-h-screen flex items-center justify-center px-6 bg-slate-50">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <img src="/LogoFiles/svg/Black logo - no background.svg" alt="Auth-Clear" className="h-12 w-auto mx-auto mb-6" />
          <h1 className="text-3xl font-semibold text-slate-800 mb-2">Authentication Disabled</h1>
          <p className="text-slate-600">Database functionality is temporarily disabled</p>
        </div>

        <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-medium text-amber-900 mb-1">Notice</h3>
              <div className="text-sm text-amber-800 space-y-1">
                <p>• Login functionality is temporarily disabled</p>
                <p>• Database backend services are not available</p>
                <p>• You can still access the onboarding demo</p>
                <p>• All features requiring authentication are disabled</p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 text-center">
          <a href="/" className="text-sm text-slate-600 hover:text-slate-800 underline">
            Return to Home
          </a>
        </div>
      </div>
    </div>
  );
};

export default Login;
