// ===================================================================
// MERCHANTS PAGE DATABASE FEATURES TEMPORARILY DISABLED
// ===================================================================
// All database-dependent functionality has been commented out
// because the database backend is temporarily disabled.
//
// To re-enable: Uncomment the database-related code below
// ===================================================================

/*
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { getMerchants } from "../services/api";
import type { RootState } from "../redux/store";

interface Merchant {
  merchant_id: string;
  legal_name: string;
  doing_business_as?: string;
  email: string;
  status: string;
  created_at: string;
}

const MerchantsPage: React.FC = () => {
  const [merchants, setMerchants] = useState<Merchant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    const fetchMerchants = async () => {
      if (!isAuthenticated) {
        setError("You must be logged in to view merchants");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        console.log("Fetching merchants for user:", user);

        const data = await getMerchants();
        console.log("Merchants API response:", data);

        // Handle both possible response formats
        const merchantsList = data.merchants || data.data || [];
        setMerchants(merchantsList);

        console.log("Merchants set:", merchantsList.length, "merchants");
      } catch (err) {
        console.error("Failed to fetch merchants:", err);
        setError(err instanceof Error ? err.message : "Failed to load merchants");
      } finally {
        setLoading(false);
      }
    };

    fetchMerchants();
  }, [isAuthenticated, user]);

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto mt-14 px-4 py-8">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h1 className="text-xl font-bold text-yellow-800">Access Denied</h1>
          <p className="text-yellow-700">You must be logged in as an admin to view merchants.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto mt-14 px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Merchants Dashboard</h1>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading merchants...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto mt-14 px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Merchants Dashboard</h1>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-red-800">Error</h2>
          <p className="text-red-700">{error}</p>
          <div className="mt-4 text-sm text-gray-600">
            <p>
              <strong>Debug info:</strong>
            </p>
            <p>User authenticated: {isAuthenticated ? "Yes" : "No"}</p>
            <p>User info: {user ? `${user.email} (${user.role})` : "None"}</p>
          </div>
          <button onClick={() => window.location.reload()} className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto mt-14 px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Merchants Dashboard</h1>
        <div className="text-sm text-gray-600">
          Welcome, {user?.name} ({user?.role})
        </div>
      </div>

      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Merchants ({merchants.length})</h2>
        </div>

        {merchants.length === 0 ? (
          <div className="px-6 py-12 text-center">
            <p className="text-gray-500">No merchants found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {merchants.map((merchant) => (
                  <tr key={merchant.merchant_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="font-medium text-gray-900">{merchant.doing_business_as || merchant.legal_name}</div>
                        {merchant.doing_business_as && <div className="text-sm text-gray-500">Legal: {merchant.legal_name}</div>}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{merchant.email}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          merchant.status === "active"
                            ? "bg-green-100 text-green-800"
                            : merchant.status === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {merchant.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(merchant.created_at).toLocaleDateString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default MerchantsPage;
*/

// Mock MerchantsPage component for when database is disabled
import React from "react";

const MerchantsPage: React.FC = () => {
  return (
    <div className="container mx-auto mt-14 px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Merchants Dashboard</h1>
      </div>

      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Database Features Disabled</h2>
        </div>

        <div className="px-6 py-12">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-amber-900 mb-1">Merchants Management Temporarily Disabled</h3>
                <div className="text-sm text-amber-800 space-y-1">
                  <p>• Merchant listing functionality is temporarily disabled</p>
                  <p>• Database backend services are not available</p>
                  <p>• Merchant data cannot be retrieved or displayed</p>
                  <p>• All merchant management features are disabled</p>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 text-center">
            <a href="/" className="text-sm text-slate-600 hover:text-slate-800 underline">
              Return to Home
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MerchantsPage;
