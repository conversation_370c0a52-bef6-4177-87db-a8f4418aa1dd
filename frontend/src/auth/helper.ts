import type { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import { toast } from "sonner";
import { store } from "../redux/index.ts";

export function setupAxios(axios: AxiosInstance) {
  axios.defaults.headers.Accept = "application/json";
  axios.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const { token } = store.getState().auth;
      console.log("token", token);

      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      return config;
    },
    async (err: AxiosError) => await Promise.reject(err)
  );

  axios.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      let errorMessage = "An unexpected error occurred";

      if (error.response) {
        switch (error.response.status) {
          case 400: {
            const data = error.response.data as { message?: string; error?: string };
            errorMessage = data.message || data.error || "Invalid request";
            break;
          }
          case 401:
            errorMessage = "Authentication failed. Please log in again.";
            // store.dispatch(signOut());
            break;
          case 403:
            errorMessage = "You do not have permission to perform this action.";
            break;
          case 404:
            errorMessage = "The requested resource was not found.";
            break;
          case 409: {
            const conflictData = error.response.data as { message?: string };
            errorMessage = conflictData.message || "Conflict occurred";
            break;
          }
          case 422: {
            const validationData = error.response.data as { message?: string; errors?: string[] };
            errorMessage = validationData.message || validationData.errors?.join(", ") || "Validation failed";
            break;
          }
          case 500:
            errorMessage = "Server error. Please try again later.";
            break;
          default: {
            const defaultData = error.response.data as { message?: string } | string;
            errorMessage = (typeof defaultData === "string" ? defaultData : defaultData?.message) || errorMessage;
          }
        }
      } else if (error.request) {
        errorMessage = "No response from server. Please check your connection.";
      }

      toast.error(errorMessage);

      return Promise.reject(error);
    }
  );
}
