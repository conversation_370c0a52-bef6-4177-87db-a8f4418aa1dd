// ===================================================================
// AUTH DEMO COMPONENT DISABLED
// ===================================================================
// This component has been disabled because authentication functionality
// is not available with direct Payrix integration.
// ===================================================================

/*
import { useState } from "react";
import { useAppDispatch, useAppSelector, signIn, signOut } from "../redux";

export const AuthDemo = () => {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, token } = useAppSelector((state) => state.auth);

  const [email, setEmail] = useState("");
  const [name, setName] = useState("");

  const handleSignIn = () => {
    if (email) {
      dispatch(
        signIn({
          user: {
            id: "123",
            email,
            name: name || undefined,
          },
          token: "demo-token-" + Date.now(),
        })
      );
    }
  };

  const handleSignOut = () => {
    dispatch(signOut());
  };

  return (
    <div className="border border-blue-200 rounded-2xl p-6 bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-3">
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-slate-800">Redux Persist Demo</h2>
      </div>

      {!isAuthenticated ? (
        <div className="space-y-4">
          <div className="text-center mb-4">
            <h3 className="text-lg font-semibold text-slate-700 mb-2">Sign In to Test Persistence</h3>
            <p className="text-slate-500 text-sm">Your data will persist across page refreshes</p>
          </div>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">Email</label>
              <input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-slate-700 placeholder-slate-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">Name (optional)</label>
              <input
                type="text"
                placeholder="Enter your name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-slate-700 placeholder-slate-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
              />
            </div>
          </div>

          <button
            onClick={handleSignIn}
            disabled={!email}
            className="w-full py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-lg hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-4 focus:ring-purple-200 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Sign In
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="text-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-green-700">Successfully Signed In!</h3>
          </div>

          <div className="bg-white rounded-xl p-4 space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-slate-600">Email:</span>
              <span className="text-sm text-slate-800 font-mono">{user?.email}</span>
            </div>

            {user?.name && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-slate-600">Name:</span>
                <span className="text-sm text-slate-800">{user.name}</span>
              </div>
            )}

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-slate-600">Token:</span>
              <span className="text-xs text-slate-600 font-mono bg-slate-100 px-2 py-1 rounded truncate max-w-32">{token}</span>
            </div>
          </div>

          <button
            onClick={handleSignOut}
            className="w-full py-2 bg-gradient-to-r from-red-500 to-red-600 text-white font-medium rounded-lg hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-4 focus:ring-red-200 transition-all duration-300"
          >
            Sign Out
          </button>
        </div>
      )}

      <div className="mt-6 p-4 bg-slate-100 rounded-xl">
        <p className="text-sm font-medium text-slate-700 mb-2">🧪 Test Redux Persist:</p>
        <ul className="text-xs text-slate-600 space-y-1">
          <li className="flex items-center">
            <svg className="w-3 h-3 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            Sign in to store user data in Redux
          </li>
          <li className="flex items-center">
            <svg className="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                clipRule="evenodd"
              />
            </svg>
            Refresh the page - data persists!
          </li>
          <li className="flex items-center">
            <svg className="w-3 h-3 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
            Check localStorage to see persisted state
          </li>
        </ul>
      </div>
    </div>
  );
};
*/

// Mock AuthDemo component for when authentication is disabled
export const AuthDemo = () => {
  return (
    <div className="max-w-md mx-auto bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl shadow-xl p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-gradient-to-br from-amber-400 to-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <h2 className="text-xl font-bold text-slate-800 mb-2">Authentication Disabled</h2>
        <p className="text-sm text-slate-600">Direct Payrix integration mode</p>
      </div>

      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <p className="text-sm text-amber-800">
          Authentication features are disabled in direct Payrix integration mode. Use the onboarding flow to submit merchant applications directly to
          Payrix.
        </p>
      </div>
    </div>
  );
};
