import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { updateFormData, nextStep, prevStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import { toast } from "sonner";

const OwnerInfoForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<Record<string, string>>({});
  // Business type logic (for future multiple owner support)
  // const selectedBusinessType = formData.type;

  // Get members array or create default
  const members = formData.merchant?.members || [
    {
      title: "",
      first: "",
      middle: "",
      last: "",
      ssn: "",
      dob: "",
      dl: "",
      dlstate: "",
      ownership: 10000, // 100% in basis points for single owner
      significantResponsibility: 1,
      politicallyExposed: 0,
      email: "",
      phone: "",
      primary: "1",
      address1: "",
      address2: "",
      city: "",
      state: "",
      zip: "",
      country: "USA",
    },
  ];

  // Get the primary member (first member)
  const member = members[0];

  // Note: Total ownership calculation can be added here for multi-owner validation

  const states = [
    "AL",
    "AK",
    "AZ",
    "AR",
    "CA",
    "CO",
    "CT",
    "DE",
    "FL",
    "GA",
    "HI",
    "ID",
    "IL",
    "IN",
    "IA",
    "KS",
    "KY",
    "LA",
    "ME",
    "MD",
    "MA",
    "MI",
    "MN",
    "MS",
    "MO",
    "MT",
    "NE",
    "NV",
    "NH",
    "NJ",
    "NM",
    "NY",
    "NC",
    "ND",
    "OH",
    "OK",
    "OR",
    "PA",
    "RI",
    "SC",
    "SD",
    "TN",
    "TX",
    "UT",
    "VT",
    "VA",
    "WA",
    "WV",
    "WI",
    "WY",
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required field validations
    if (!member.first?.trim()) newErrors.first = "First name is required";
    if (!member.last?.trim()) newErrors.last = "Last name is required";
    if (!member.title?.trim()) newErrors.title = "Business title is required";
    if (!member.email?.trim()) newErrors.email = "Email is required";
    if (!member.phone?.trim()) newErrors.phone = "Phone is required";
    if (!member.address1?.trim()) newErrors.address1 = "Address is required";
    if (!member.city?.trim()) newErrors.city = "City is required";
    if (!member.state?.trim()) newErrors.state = "State is required";
    if (!member.zip?.trim()) newErrors.zip = "ZIP code is required";
    if (!member.dob?.trim()) newErrors.dob = "Date of birth is required";
    if (!member.ssn?.trim()) newErrors.ssn = "SSN is required";

    // Driver's license validation (required for KYC)
    if (!member.dl?.trim()) newErrors.dl = "Driver's license number is required";
    if (!member.dlstate?.trim()) newErrors.dlstate = "Driver's license state is required";

    // Account creation validation (mandatory)
    if (!formData.username?.trim()) newErrors.username = "Username is required";
    if (!formData.password?.trim()) newErrors.password = "Password is required";
    if (formData.password && formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    // Format validations
    if (member.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(member.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (member.phone && !/^\d{10}$/.test(member.phone.replace(/\D/g, ""))) {
      newErrors.phone = "Phone must be 10 digits";
    }

    if (member.ssn && !/^\d{9}$/.test(member.ssn.replace(/\D/g, ""))) {
      newErrors.ssn = "SSN must be 9 digits";
    }

    if (member.zip && !/^\d{5}(-\d{4})?$/.test(member.zip)) {
      newErrors.zip = "ZIP code must be 5 digits or 5-4 format";
    }

    if (member.dlstate && !/^[A-Z]{2}$/.test(member.dlstate)) {
      newErrors.dlstate = "Driver's license state must be a valid 2-letter state code";
    }

    // Username validation (mandatory)
    if (formData.username) {
      if (!/^[a-z0-9]{3,50}$/.test(formData.username)) {
        newErrors.username = "Username must be 3-50 characters and contain only lowercase letters and numbers (no uppercase or special characters)";
      } else if (!/(?=.*\d)/.test(formData.username)) {
        newErrors.username = "Username must include at least one number";
      }
    }

    // Password validation (mandatory)
    if (formData.password) {
      const passwordErrors = [];
      if (formData.password.length < 8) passwordErrors.push("at least 8 characters");
      if (!/(?=.*[a-z])/.test(formData.password)) passwordErrors.push("one lowercase letter");
      if (!/(?=.*[A-Z])/.test(formData.password)) passwordErrors.push("one uppercase letter");
      if (!/(?=.*\d)/.test(formData.password)) passwordErrors.push("one number");
      if (!/(?=.*[@$!%*?&])/.test(formData.password)) passwordErrors.push("one special character (@$!%*?&)");

      if (passwordErrors.length > 0) {
        newErrors.password = `Password must include: ${passwordErrors.join(", ")}`;
      }
    }

    // Date of birth validation (must be at least 18 years old)
    if (member.dob) {
      const dobDate = new Date(member.dob);
      const today = new Date();
      const age = today.getFullYear() - dobDate.getFullYear();
      const monthDiff = today.getMonth() - dobDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dobDate.getDate())) {
        if (age - 1 < 18) {
          newErrors.dob = "Owner must be at least 18 years old";
        }
      } else if (age < 18) {
        newErrors.dob = "Owner must be at least 18 years old";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      dispatch(nextStep());
    } else {
      const errorCount = Object.keys(errors).length;
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""} before continuing.`);
    }
  };

  const handleChange = (memberIndex: number, field: string, value: string | number) => {
    const updatedMembers = [...members];
    updatedMembers[memberIndex] = {
      ...updatedMembers[memberIndex],
      [field]: value,
    };

    // Note: Address auto-population could be added here if needed

    dispatch(
      updateFormData({
        merchant: {
          dba: "",
          new: 1,
          mcc: "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  // Note: addOwner and removeOwner functions can be added here when multiple owner support is needed

  const handleAccountFieldChange = (field: string, value: string | boolean) => {
    dispatch(updateFormData({ [field]: value, createAccount: true }));

    // Real-time validation for username and password fields
    if (typeof value === "string") {
      const newErrors = { ...errors };

      if (field === "username") {
        if (value) {
          if (!/^[a-z0-9]{3,50}$/.test(value)) {
            newErrors.username =
              "Username must be 3-50 characters and contain only lowercase letters and numbers (no uppercase or special characters)";
          } else if (!/(?=.*\d)/.test(value)) {
            newErrors.username = "Username must include at least one number";
          } else {
            delete newErrors.username;
          }
        } else {
          delete newErrors.username;
        }
      }

      if (field === "password") {
        if (value) {
          const passwordErrors = [];
          if (value.length < 8) passwordErrors.push("at least 8 characters");
          if (!/(?=.*[a-z])/.test(value)) passwordErrors.push("one lowercase letter");
          if (!/(?=.*[A-Z])/.test(value)) passwordErrors.push("one uppercase letter");
          if (!/(?=.*\d)/.test(value)) passwordErrors.push("one number");
          if (!/(?=.*[@$!%*?&])/.test(value)) passwordErrors.push("one special character (@$!%*?&)");

          if (passwordErrors.length > 0) {
            newErrors.password = `Password must include: ${passwordErrors.join(", ")}`;
          } else {
            delete newErrors.password;
          }
        } else {
          delete newErrors.password;
        }

        // Also validate confirm password if it exists
        if (formData.confirmPassword && value !== formData.confirmPassword) {
          newErrors.confirmPassword = "Passwords do not match";
        } else if (formData.confirmPassword && value === formData.confirmPassword) {
          delete newErrors.confirmPassword;
        }
      }

      if (field === "confirmPassword") {
        if (value && formData.password && value !== formData.password) {
          newErrors.confirmPassword = "Passwords do not match";
        } else {
          delete newErrors.confirmPassword;
        }
      }

      setErrors(newErrors);
    }
  };

  const formatSSN = (value: string) => {
    const digits = value.replace(/\D/g, "");
    if (digits.length <= 3) return digits;
    if (digits.length <= 5) return `${digits.slice(0, 3)}-${digits.slice(3)}`;
    return `${digits.slice(0, 3)}-${digits.slice(3, 5)}-${digits.slice(5, 9)}`;
  };

  const formatPhone = (value: string) => {
    const digits = value.replace(/\D/g, "");
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Owner Information</h1>
            <p className="text-gray-600 mt-1">Details about the primary business owner</p>
          </div>

          <form onSubmit={handleSubmit} className="px-8 py-8">
            {/* Personal Information */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Personal Information</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                  <input
                    type="text"
                    value={member.first || ""}
                    onChange={(e) => handleChange(0, "first", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.first ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="John"
                  />
                  {errors.first && <p className="text-red-600 text-sm mt-1">{errors.first}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Middle Name</label>
                  <input
                    type="text"
                    value={member.middle || ""}
                    onChange={(e) => handleChange(0, "middle", e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Optional"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                  <input
                    type="text"
                    value={member.last || ""}
                    onChange={(e) => handleChange(0, "last", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.last ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="Smith"
                  />
                  {errors.last && <p className="text-red-600 text-sm mt-1">{errors.last}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Title *</label>
                  <input
                    type="text"
                    value={member.title || ""}
                    onChange={(e) => handleChange(0, "title", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.title ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="CEO, Owner, President, etc."
                  />
                  {errors.title && <p className="text-red-600 text-sm mt-1">{errors.title}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth *</label>
                  <input
                    type="date"
                    value={
                      member.dob
                        ? member.dob.length === 8
                          ? `${member.dob.slice(0, 4)}-${member.dob.slice(4, 6)}-${member.dob.slice(6, 8)}`
                          : member.dob
                        : ""
                    }
                    onChange={(e) => handleChange(0, "dob", e.target.value.replace(/-/g, ""))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.dob ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                  />
                  {errors.dob && <p className="text-red-600 text-sm mt-1">{errors.dob}</p>}
                  <p className="text-gray-500 text-sm mt-1">Must be at least 18 years old. Date will be stored in YYYYMMDD format.</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Social Security Number *</label>
                  <input
                    type="text"
                    value={formatSSN(member.ssn || "")}
                    onChange={(e) => handleChange(0, "ssn", e.target.value.replace(/\D/g, ""))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.ssn ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="***********"
                    maxLength={11}
                  />
                  {errors.ssn && <p className="text-red-600 text-sm mt-1">{errors.ssn}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Ownership Percentage *</label>
                  <div className="relative">
                    <input
                      type="number"
                      min="1"
                      max="100"
                      value={Math.round((member.ownership || 10000) / 100)}
                      onChange={(e) => handleChange(0, "ownership", parseInt(e.target.value) * 100 || 10000)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-8"
                      placeholder="100"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span className="text-gray-500">%</span>
                    </div>
                  </div>
                  <p className="text-gray-500 text-sm mt-1">Percentage of business ownership</p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Contact Information</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Personal Email *</label>
                  <input
                    type="email"
                    value={member.email || ""}
                    onChange={(e) => handleChange(0, "email", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.email ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email}</p>}
                  <p className="text-gray-500 text-sm mt-1">Personal email, not business email</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Personal Phone *</label>
                  <input
                    type="tel"
                    value={formatPhone(member.phone || "")}
                    onChange={(e) => handleChange(0, "phone", e.target.value.replace(/\D/g, ""))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.phone ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="(*************"
                    maxLength={14}
                  />
                  {errors.phone && <p className="text-red-600 text-sm mt-1">{errors.phone}</p>}
                </div>
              </div>
            </div>

            {/* Personal Address */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Personal Address</h2>
              <p className="text-gray-600 mb-6">Enter your home address (not business address)</p>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Street Address *</label>
                  <input
                    type="text"
                    value={member.address1 || ""}
                    onChange={(e) => handleChange(0, "address1", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.address1 ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="123 Home Street"
                  />
                  {errors.address1 && <p className="text-red-600 text-sm mt-1">{errors.address1}</p>}
                </div>

                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Address Line 2</label>
                  <input
                    type="text"
                    value={member.address2 || ""}
                    onChange={(e) => handleChange(0, "address2", e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Apt, suite, etc. (optional)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
                  <input
                    type="text"
                    value={member.city || ""}
                    onChange={(e) => handleChange(0, "city", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.city ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="Los Angeles"
                  />
                  {errors.city && <p className="text-red-600 text-sm mt-1">{errors.city}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
                  <select
                    value={member.state || ""}
                    onChange={(e) => handleChange(0, "state", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.state ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                  >
                    <option value="">Select state</option>
                    {states.map((state) => (
                      <option key={state} value={state}>
                        {state}
                      </option>
                    ))}
                  </select>
                  {errors.state && <p className="text-red-600 text-sm mt-1">{errors.state}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ZIP Code *</label>
                  <input
                    type="text"
                    value={member.zip || ""}
                    onChange={(e) => handleChange(0, "zip", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.zip ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="90210"
                  />
                  {errors.zip && <p className="text-red-600 text-sm mt-1">{errors.zip}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
                  <input type="text" value="USA" readOnly className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50" />
                </div>
              </div>
            </div>

            {/* Driver's License (Required for KYC) */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Driver&apos;s License Information</h2>
              <p className="text-gray-600 mb-6">Required for identity verification and KYC compliance</p>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Driver&apos;s License Number *</label>
                  <input
                    type="text"
                    value={member.dl || ""}
                    onChange={(e) => handleChange(0, "dl", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.dl ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="********"
                  />
                  {errors.dl && <p className="text-red-600 text-sm mt-1">{errors.dl}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Issuing State *</label>
                  <select
                    value={member.dlstate || ""}
                    onChange={(e) => handleChange(0, "dlstate", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.dlstate ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                  >
                    <option value="">Select state</option>
                    {states.map((state) => (
                      <option key={state} value={state}>
                        {state}
                      </option>
                    ))}
                  </select>
                  {errors.dlstate && <p className="text-red-600 text-sm mt-1">{errors.dlstate}</p>}
                </div>
              </div>
            </div>

            {/* Responsibilities & Status */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Responsibilities & Status</h2>
              <div className="space-y-6">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={member.significantResponsibility === 1}
                      onChange={(e) => handleChange(0, "significantResponsibility", e.target.checked ? 1 : 0)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label className="text-sm font-medium text-gray-700">This person has significant responsibility for the business</label>
                  </div>
                  <p className="text-gray-600 text-sm mt-2 ml-7">
                    Check this if the person is a CEO, CFO, Owner, VP, managing member, or similar controlling authority.
                  </p>
                </div>

                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={member.politicallyExposed === 1}
                      onChange={(e) => handleChange(0, "politicallyExposed", e.target.checked ? 1 : 0)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label className="text-sm font-medium text-gray-700">This person is politically exposed</label>
                  </div>
                  <p className="text-gray-600 text-sm mt-2 ml-7">
                    A politically exposed person is someone who, through their prominent position or influence, is more susceptible to being involved
                    in bribery or corruption.
                  </p>
                </div>
              </div>
            </div>

            {/* Account Creation */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Auth Clear Portal Account</h2>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <p className="text-gray-700 text-sm">
                  A Auth Clear portal account will be created for this merchant, allowing access to their dashboard to view transactions, manage
                  settings, and access reports.
                </p>
              </div>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                    <input
                      type="text"
                      value={formData.username || ""}
                      onChange={(e) => handleAccountFieldChange("username", e.target.value.toLowerCase())}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.username ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="merchant123"
                    />
                    {errors.username && <p className="text-red-600 text-sm mt-1">{errors.username}</p>}
                    {!errors.username && (
                      <p className="text-gray-500 text-xs mt-1">
                        3-50 characters, lowercase letters and at least one number only (no uppercase or special characters)
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                    <input
                      type="password"
                      value={formData.password || ""}
                      onChange={(e) => handleAccountFieldChange("password", e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.password ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="Create a strong password"
                    />
                    {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password}</p>}
                    {!errors.password && (
                      <p className="text-gray-500 text-xs mt-1">Min 8 chars: uppercase, lowercase, number, special character (@$!%*?&)</p>
                    )}
                  </div>

                  <div className="lg:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Confirm Password *</label>
                    <input
                      type="password"
                      value={formData.confirmPassword || ""}
                      onChange={(e) => handleAccountFieldChange("confirmPassword", e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.confirmPassword ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="Confirm your password"
                    />
                    {errors.confirmPassword && <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>}
                  </div>
                </div>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <svg className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <div className="text-sm text-amber-800">
                      <p className="font-medium mb-1">Account Creation Notice</p>
                      <p>
                        The account will be created after successful merchant onboarding. You will receive login credentials and a link to access your
                        Auth Clear portal dashboard.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
              >
                Previous
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Continue to Bank Account
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default OwnerInfoForm;
