import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fillDemoData, nextStep, updateFormData } from "../../redux/slices/onboardingSlice.ts";
import { type RootState } from "../../redux/store.ts";
import { toast } from "sonner";

const BusinessInfoForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const businessTypes = [
    { value: 1, label: "Sole Proprietor" },
    { value: 2, label: "Corporation" },
    { value: 3, label: "Limited Liability Company" },
    { value: 4, label: "Partnership" },
  ];

  // Dynamic field visibility based on business type
  const selectedBusinessType = formData.type;
  const isSoleProprietor = selectedBusinessType === 1;
  const isCorporation = selectedBusinessType === 2;
  const isLLC = selectedBusinessType === 3;
  const isPartnership = selectedBusinessType === 4;
  const requiresCorporateStructure = isCorporation || isLLC || isPartnership;

  // Real-time validation effect
  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      // Clear errors that are no longer relevant
      const newErrors = { ...errors };
      let hasChanges = false;

      // Clear DBA error if no longer required
      if (!requiresCorporateStructure && newErrors.dba) {
        delete newErrors.dba;
        hasChanges = true;
      }

      if (hasChanges) {
        setErrors(newErrors);
      }
    }
  }, [selectedBusinessType, requiresCorporateStructure, errors]);

  const states = [
    "AL",
    "AK",
    "AZ",
    "AR",
    "CA",
    "CO",
    "CT",
    "DE",
    "FL",
    "GA",
    "HI",
    "ID",
    "IL",
    "IN",
    "IA",
    "KS",
    "KY",
    "LA",
    "ME",
    "MD",
    "MA",
    "MI",
    "MN",
    "MS",
    "MO",
    "MT",
    "NE",
    "NV",
    "NH",
    "NJ",
    "NM",
    "NY",
    "NC",
    "ND",
    "OH",
    "OK",
    "OR",
    "PA",
    "RI",
    "SC",
    "SD",
    "TN",
    "TX",
    "UT",
    "VT",
    "VA",
    "WA",
    "WV",
    "WI",
    "WY",
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required field validations
    if (!formData.name?.trim()) newErrors.name = "Legal business name is required";
    if (!formData.type) newErrors.type = "Business type is required";
    if (!formData.ein?.trim()) newErrors.ein = isSoleProprietor ? "Tax ID/EIN or SSN is required" : "Tax ID/EIN is required";
    if (!formData.website?.trim()) newErrors.website = "Website is required";
    if (!formData.email?.trim()) newErrors.email = "Business email is required";
    if (!formData.phone?.trim()) newErrors.phone = "Business phone is required";
    if (!formData.address1?.trim()) newErrors.address1 = "Address is required";
    if (!formData.city?.trim()) newErrors.city = "City is required";
    if (!formData.state?.trim()) newErrors.state = "State is required";
    if (!formData.zip?.trim()) newErrors.zip = "ZIP code is required";
    if (!formData.merchant?.mcc?.trim()) newErrors.mcc = "MCC code is required";
    if (!formData.merchant?.established?.trim()) newErrors.established = "Business establishment date is required";

    // DBA validation - required for corporate structures
    if (requiresCorporateStructure && !formData.merchant?.dba?.trim()) {
      newErrors.dba = "DBA/Statement descriptor is required for this business type";
    }

    // Format validations
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (formData.phone && !/^\d{10}$/.test(formData.phone.replace(/\D/g, ""))) {
      newErrors.phone = "Phone must be 10 digits";
    }

    if (formData.ein && !/^\d{9}$/.test(formData.ein.replace(/\D/g, ""))) {
      newErrors.ein = "EIN must be 9 digits";
    }

    if (formData.zip && !/^\d{5}(-\d{4})?$/.test(formData.zip)) {
      newErrors.zip = "ZIP code must be 5 digits or 5-4 format";
    }

    // MCC validation for merchant object
    if (formData.merchant?.mcc && !/^\d{4}$/.test(formData.merchant.mcc)) {
      newErrors.mcc = "MCC must be 4 digits";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // toast.success("Business information saved successfully!");
      dispatch(nextStep());
    } else {
      const errorCount = Object.keys(errors).length;
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""} before continuing.`);
    }
  };

  const handleChange = (field: string, value: string | number) => {
    if (field.startsWith("merchant.")) {
      const merchantField = field.replace("merchant.", "");
      dispatch(
        updateFormData({
          merchant: {
            dba: "",
            new: 1,
            mcc: "",
            status: "1",
            members: [],
            ...formData.merchant,
            [merchantField]: value,
          },
        })
      );
    } else {
      const updates: Record<string, string | number | object> = { [field]: value };

      // Auto-populate DBA with business name if DBA is empty and business type requires it
      if (field === "name" && requiresCorporateStructure && !formData.merchant?.dba) {
        updates.merchant = {
          new: 1,
          mcc: "",
          status: "1",
          ...formData.merchant,
          dba: value as string,
        };
      }

      // Set public flag based on business type
      if (field === "type") {
        updates.public = value === 2 ? 1 : 0; // Corporation = public, others = private
      }

      dispatch(updateFormData(updates));
    }
  };

  const handleFillDemoData = () => {
    dispatch(fillDemoData());
  };

  const formatEIN = (value: string) => {
    const digits = value.replace(/\D/g, "");
    if (digits.length <= 2) return digits;
    return `${digits.slice(0, 2)}-${digits.slice(2, 9)}`;
  };

  const formatPhone = (value: string) => {
    const digits = value.replace(/\D/g, "");
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Business Information</h1>
              <p className="text-gray-600 mt-1">Tell us about your business</p>
            </div>
            <button
              type="button"
              onClick={handleFillDemoData}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-colors"
              title="Fill form with demo data for testing"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Fill Demo Data
            </button>
          </div>

          <form onSubmit={handleSubmit} className="px-8 py-8">
            {/* Business Details */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Business Details</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Legal Business Name *</label>
                  <input
                    type="text"
                    value={formData.name || ""}
                    onChange={(e) => handleChange("name", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.name ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="Enter your legal business name"
                  />
                  {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    DBA / Statement Descriptor {requiresCorporateStructure && "*"}
                  </label>
                  <input
                    type="text"
                    value={formData.merchant?.dba || ""}
                    onChange={(e) => handleChange("merchant.dba", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.dba ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder={isSoleProprietor ? "Optional - how your business appears on statements" : "How your business appears on statements"}
                  />
                  {errors.dba && <p className="text-red-600 text-sm mt-1">{errors.dba}</p>}
                  <p className="text-gray-500 text-sm mt-1">
                    {isSoleProprietor
                      ? "Optional - defaults to legal name if not provided"
                      : "Required - how your business appears on customer statements"}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Type *</label>
                  <select
                    value={formData.type || ""}
                    onChange={(e) => handleChange("type", parseInt(e.target.value))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.type ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                  >
                    <option value="">Select business type</option>
                    {businessTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  {errors.type && <p className="text-red-600 text-sm mt-1">{errors.type}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {isSoleProprietor ? "Tax ID / EIN or SSN *" : "Tax ID / EIN *"}
                  </label>
                  <input
                    type="text"
                    value={formatEIN(formData.ein || "")}
                    onChange={(e) => handleChange("ein", e.target.value.replace(/\D/g, ""))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.ein ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder={isSoleProprietor ? "12-3456789 or ***********" : "12-3456789"}
                    maxLength={isSoleProprietor ? 11 : 10}
                  />
                  {errors.ein && <p className="text-red-600 text-sm mt-1">{errors.ein}</p>}
                  <p className="text-gray-500 text-sm mt-1">
                    {isSoleProprietor ? "9-digit EIN or 9-digit SSN (sole proprietors may use either)" : "9-digit EIN required for business entities"}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Website *</label>
                  <input
                    type="url"
                    value={formData.website || ""}
                    onChange={(e) => handleChange("website", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.website ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="https://www.yourbusiness.com"
                  />
                  {errors.website && <p className="text-red-600 text-sm mt-1">{errors.website}</p>}
                  <p className="text-gray-500 text-sm mt-1">Use https://nowebsite.com if you don&apos;t have one</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">MCC Code (Merchant Category Code) *</label>
                  <input
                    type="text"
                    value={formData.merchant?.mcc || ""}
                    onChange={(e) => handleChange("merchant.mcc", e.target.value.replace(/\D/g, ""))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.mcc ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="5999"
                    maxLength={4}
                  />
                  {errors.mcc && <p className="text-red-600 text-sm mt-1">{errors.mcc}</p>}
                  <p className="text-gray-500 text-sm mt-1">4-digit code describing your business type</p>
                </div>

                {/* Annual Credit Card Sales - Hidden field (Payrix only accepts 0) */}
                <input type="hidden" value={0} onChange={() => handleChange("merchant.annualCCSales", 0)} />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Average Transaction Amount</label>
                  <input
                    type="number"
                    value={formData.merchant?.avgTicket || ""}
                    onChange={(e) => handleChange("merchant.avgTicket", e.target.value ? parseInt(e.target.value) : 0)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="50"
                    min="0"
                  />
                  <p className="text-gray-500 text-sm mt-1">Average amount per transaction in USD</p>
                </div>

                {/* Established Date - Required for all business types */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Established Date *</label>
                  <input
                    type="date"
                    value={
                      formData.merchant?.established
                        ? `${formData.merchant.established.slice(0, 4)}-${formData.merchant.established.slice(
                            4,
                            6
                          )}-${formData.merchant.established.slice(6, 8)}`
                        : ""
                    }
                    onChange={(e) => {
                      const dateValue = e.target.value.replace(/-/g, ""); // Convert YYYY-MM-DD to YYYYMMDD
                      handleChange("merchant.established", dateValue);
                    }}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.established ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    max={new Date().toISOString().split("T")[0]} // Can't be in the future
                  />
                  {errors.established && <p className="text-red-600 text-sm mt-1">{errors.established}</p>}
                  <p className="text-gray-500 text-sm mt-1">When was your business legally established?</p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Contact Information</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Email *</label>
                  <input
                    type="email"
                    value={formData.email || ""}
                    onChange={(e) => handleChange("email", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.email ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Business Phone *</label>
                  <input
                    type="tel"
                    value={formatPhone(formData.phone || "")}
                    onChange={(e) => handleChange("phone", e.target.value.replace(/\D/g, ""))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.phone ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="(*************"
                    maxLength={14}
                  />
                  {errors.phone && <p className="text-red-600 text-sm mt-1">{errors.phone}</p>}
                </div>
              </div>
            </div>

            {/* Business Address */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Business Address</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Street Address *</label>
                  <input
                    type="text"
                    value={formData.address1 || ""}
                    onChange={(e) => handleChange("address1", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.address1 ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="123 Business Street"
                  />
                  {errors.address1 && <p className="text-red-600 text-sm mt-1">{errors.address1}</p>}
                </div>

                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Address Line 2</label>
                  <input
                    type="text"
                    value={formData.address2 || ""}
                    onChange={(e) => handleChange("address2", e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Suite, floor, etc. (optional)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
                  <input
                    type="text"
                    value={formData.city || ""}
                    onChange={(e) => handleChange("city", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.city ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="Los Angeles"
                  />
                  {errors.city && <p className="text-red-600 text-sm mt-1">{errors.city}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
                  <select
                    value={formData.state || ""}
                    onChange={(e) => handleChange("state", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.state ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                  >
                    <option value="">Select state</option>
                    {states.map((state) => (
                      <option key={state} value={state}>
                        {state}
                      </option>
                    ))}
                  </select>
                  {errors.state && <p className="text-red-600 text-sm mt-1">{errors.state}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ZIP Code *</label>
                  <input
                    type="text"
                    value={formData.zip || ""}
                    onChange={(e) => handleChange("zip", e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.zip ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="90210"
                  />
                  {errors.zip && <p className="text-red-600 text-sm mt-1">{errors.zip}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
                  <input
                    type="text"
                    value={formData.country || "USA"}
                    onChange={(e) => handleChange("country", e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="USA"
                  />
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="border-t border-gray-200 pt-6 flex justify-end">
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Continue to Owner Information
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BusinessInfoForm;
