import { useState, useEffect } from "react";

interface NavSection {
  id: string;
  title: string;
  subsections?: { id: string; title: string }[];
}

interface SidebarNavigationProps {
  sections: NavSection[];
  className?: string;
}

export const SidebarNavigation = ({ sections, className = "" }: SidebarNavigationProps) => {
  const [activeSection, setActiveSection] = useState<string>("");
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
          }
        });
      },
      {
        rootMargin: "-20% 0px -70% 0px",
        threshold: 0.1,
      }
    );

    // Observe all sections
    sections.forEach((section) => {
      const element = document.getElementById(section.id);
      if (element) observer.observe(element);

      // Also observe subsections
      section.subsections?.forEach((subsection) => {
        const subElement = document.getElementById(subsection.id);
        if (subElement) observer.observe(subElement);
      });
    });

    return () => observer.disconnect();
  }, [sections]);

  useEffect(() => {
    // Show sidebar after a brief delay for smooth entrance
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offset = 100; // Account for fixed header
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - offset;

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  };

  const isActive = (sectionId: string) => activeSection === sectionId;

  return (
    <div className={`${className?.includes('relative') ? 'relative' : 'fixed left-6 top-24 w-64 z-30'} ${className} ${isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-4"} transition-all duration-500 ease-out`}>
      <div className="bg-white/95 backdrop-blur-md rounded-xl shadow-xl border border-slate-200 p-6 max-h-[calc(100vh-8rem)] overflow-y-auto">
        <h3 className="text-lg font-semibold text-slate-800 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
          </svg>
          Table of Contents
        </h3>

        <nav className="space-y-2">
          {sections.map((section) => (
            <div key={section.id}>
              <button
                onClick={() => scrollToSection(section.id)}
                className={`w-full text-left px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center group ${
                  isActive(section.id) ? "bg-blue-100 text-blue-700 shadow-sm" : "text-slate-600 hover:text-blue-600 hover:bg-blue-50"
                }`}
              >
                <div
                  className={`w-2 h-2 rounded-full mr-3 transition-all duration-200 ${
                    isActive(section.id) ? "bg-blue-600" : "bg-slate-300 group-hover:bg-blue-400"
                  }`}
                />
                <span className="text-sm">{section.title}</span>
              </button>

              {section.subsections && (
                <div className="ml-5 mt-1 space-y-1">
                  {section.subsections.map((subsection) => (
                    <button
                      key={subsection.id}
                      onClick={() => scrollToSection(subsection.id)}
                      className={`w-full text-left px-3 py-1.5 rounded-md text-xs transition-all duration-200 flex items-center group ${
                        isActive(subsection.id) ? "bg-blue-50 text-blue-600" : "text-slate-500 hover:text-blue-500 hover:bg-blue-50"
                      }`}
                    >
                      <div
                        className={`w-1.5 h-1.5 rounded-full mr-2 transition-all duration-200 ${
                          isActive(subsection.id) ? "bg-blue-500" : "bg-slate-300 group-hover:bg-blue-300"
                        }`}
                      />
                      {subsection.title}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* Scroll to top button */}
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
          className="w-full mt-6 px-3 py-2 text-sm text-slate-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 flex items-center justify-center group"
        >
          <svg
            className="w-4 h-4 mr-2 group-hover:-translate-y-0.5 transition-transform duration-200"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
          Back to Top
        </button>
      </div>
    </div>
  );
};
