import { Navigate, Routes } from "react-router-dom";
import { useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { type UserRole, type UserType, UserRole as UserRoleEnum, UserType as UserTypeEnum } from "../../types/auth.ts";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredUserTypes?: UserType[];
  requiredRoles?: UserRole[];
  requirePlatformAdmin?: boolean;
  requireMerchantAccess?: boolean;
  fallbackPath?: string;
}

const ProtectedRoute = ({
  children,
  requiredUserTypes = [],
  requiredRoles = [],
  requirePlatformAdmin = false,
  requireMerchantAccess = false,
  fallbackPath = "/unauthorized",
}: ProtectedRouteProps) => {
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />;
  }

  if (requirePlatformAdmin) {
    const isPlatformAdmin =
      user.userType === UserTypeEnum.PLATFORM_ADMIN && (user.role === UserRoleEnum.ADMIN || user.role === UserRoleEnum.SUPER_ADMIN);

    if (!isPlatformAdmin) {
      return <Navigate to={fallbackPath} replace />;
    }
  }

  if (requireMerchantAccess) {
    const isMerchantUser = user.userType === UserTypeEnum.MERCHANT_USER && user.merchantId;

    if (!isMerchantUser) {
      return <Navigate to={fallbackPath} replace />;
    }
  }

  if (requiredUserTypes.length > 0 && user.userType && !requiredUserTypes.includes(user.userType as UserType)) {
    return <Navigate to={fallbackPath} replace />;
  }

  if (requiredRoles.length > 0 && user.role && !requiredRoles.includes(user.role as UserRole)) {
    return <Navigate to={fallbackPath} replace />;
  }

  return <Routes>{children}</Routes>;
};

export default ProtectedRoute;
