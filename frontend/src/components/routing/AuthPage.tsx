// ===================================================================
// AUTH PAGE TEMPORARILY DISABLED
// ===================================================================
// This entire authentication page has been commented out because
// the database backend is temporarily disabled.
//
// To re-enable: Uncomment all the code below
// ===================================================================

/*
import { Routes, Route, Link, Navigate } from "react-router-dom";
import { useAppSelector, useAppDispatch, signOut } from "../../redux";

const AuthDashboard = () => {
  const dispatch = useAppDispatch();
  const { user, token } = useAppSelector((state) => state.auth);

  const handleSignOut = () => {
    dispatch(signOut());
  };

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <div style={{ marginBottom: "30px" }}>
        <Link to="/" style={{ color: "#007bff", textDecoration: "none" }}>
          ← Back to Home
        </Link>
      </div>

      <h2>Authentication Dashboard</h2>

      <div
        style={{
          backgroundColor: "white",
          padding: "30px",
          borderRadius: "8px",
          boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
          marginBottom: "30px",
        }}
      >
        <h3>User Information</h3>
        <div style={{ marginBottom: "15px" }}>
          <strong>ID:</strong> {user?.id}
        </div>
        <div style={{ marginBottom: "15px" }}>
          <strong>Email:</strong> {user?.email}
        </div>
        {user?.name && (
          <div style={{ marginBottom: "15px" }}>
            <strong>Name:</strong> {user.name}
          </div>
        )}
        <div style={{ marginBottom: "20px" }}>
          <strong>Token:</strong>
          <code
            style={{
              backgroundColor: "#f8f9fa",
              padding: "4px 8px",
              borderRadius: "4px",
              marginLeft: "8px",
              fontSize: "12px",
              wordBreak: "break-all",
            }}
          >
            {token}
          </code>
        </div>

        <button
          onClick={handleSignOut}
          style={{
            padding: "10px 20px",
            backgroundColor: "#dc3545",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Sign Out
        </button>
      </div>

      <div
        style={{
          backgroundColor: "#f8f9fa",
          padding: "30px",
          borderRadius: "8px",
        }}
      >
        <h3>Authentication Features</h3>
        <ul style={{ lineHeight: "1.8" }}>
          <li>
            <strong>Persistent State:</strong> Your login state is saved in localStorage
          </li>
          <li>
            <strong>Token Management:</strong> JWT token is stored securely
          </li>
          <li>
            <strong>Protected Routes:</strong> Access control for authenticated users
          </li>
          <li>
            <strong>Auto Redirect:</strong> Automatic navigation based on auth state
          </li>
        </ul>

        <div style={{ marginTop: "20px" }}>
          <h4>Quick Actions</h4>
          <div style={{ display: "flex", gap: "10px", flexWrap: "wrap" }}>
            <Link to="/onboarding">
              <button
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#007bff",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                Start Onboarding
              </button>
            </Link>
            <Link to="/payment">
              <button
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                Make Payment
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

const AuthSettings = () => {
  return (
    <div style={{ padding: "20px", maxWidth: "600px", margin: "0 auto" }}>
      <div style={{ marginBottom: "30px" }}>
        <Link to="/auth" style={{ color: "#007bff", textDecoration: "none" }}>
          ← Back to Auth Dashboard
        </Link>
      </div>

      <h2>Authentication Settings</h2>

      <div
        style={{
          backgroundColor: "white",
          padding: "30px",
          borderRadius: "8px",
          boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
        }}
      >
        <h3>Security Settings</h3>
        <div style={{ marginBottom: "20px" }}>
          <label style={{ display: "block", marginBottom: "8px" }}>Two-Factor Authentication</label>
          <button
            style={{
              padding: "8px 16px",
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Enable 2FA
          </button>
        </div>

        <div style={{ marginBottom: "20px" }}>
          <label style={{ display: "block", marginBottom: "8px" }}>Session Management</label>
          <button
            style={{
              padding: "8px 16px",
              backgroundColor: "#dc3545",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Clear All Sessions
          </button>
        </div>

        <div>
          <label style={{ display: "block", marginBottom: "8px" }}>Password</label>
          <button
            style={{
              padding: "8px 16px",
              backgroundColor: "#007bff",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Change Password
          </button>
        </div>
      </div>
    </div>
  );
};

const AuthPage = () => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return (
    <Routes>
      <Route path="/" element={<AuthDashboard />} />
      <Route path="/settings" element={<AuthSettings />} />
      <Route path="*" element={<Navigate to="/auth" replace />} />
    </Routes>
  );
};

export default AuthPage;
*/

// Mock AuthPage component for when authentication is disabled
// import React from "react";
import { Navigate } from "react-router-dom";

const AuthPage = () => {
  // Redirect to home since authentication is disabled
  return <Navigate to="/" replace />;
};

export default AuthPage;
