// ===================================================================
// ROLE-BASED ROUTES TEMPORARILY DISABLED
// ===================================================================
// All authentication and role-based routing has been commented out
// because the database backend is temporarily disabled.
//
// To re-enable: Uncomment the authentication-based routing below
// ===================================================================

/*
import { useSelector } from "react-redux";
import { Navigate, Route } from "react-router-dom";
import { type RootState } from "../../redux/store.ts";
import { UserType } from "../../types/auth.ts";
import ProtectedRoute from "./ProtectedRoute.tsx";

import { Home, MerchantsPage, UnauthorizedPage } from "../../pages/index.ts";

import { MerchantDashboard, PaymentPage } from "../../pages/index.ts";
import ErrorsRouting from "./ErrorsRouting.tsx";

const RoleBasedRoutes = () => {
  const { user } = useSelector((state: RootState) => state.auth);

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (user.userType === UserType.PLATFORM_ADMIN) {
    return (
      <ProtectedRoute requirePlatformAdmin>
        <Route path="/" element={<Home />} />
        <Route path="/merchants" element={<MerchantsPage />} />
        <Route path="/payment" element={<PaymentPage />} />

        <Route path="/unauthorized" element={<UnauthorizedPage />} />
        <Route path="/error/*" element={<ErrorsRouting />} />
        <Route path="*" element={<Navigate to="/error/404" />} />
      </ProtectedRoute>
    );
  }

  if (user.userType === UserType.MERCHANT_USER) {
    return (
      <ProtectedRoute requireMerchantAccess>
        <Route path="/" element={<Home />} />
        <Route path="/payment" element={<PaymentPage />} />
        <Route path="/dashboard" element={<MerchantDashboard />} />

        <Route path="/unauthorized" element={<UnauthorizedPage />} />
        <Route path="/error/*" element={<ErrorsRouting />} />
        <Route path="*" element={<Navigate to="/error/404" />} />
      </ProtectedRoute>
    );
  }

  return <Navigate to="/unauthorized" replace />;
};
*/

// Simplified routing without authentication requirements
import { Navigate, Route, Routes } from "react-router-dom";
import { Home, MerchantsPage, UnauthorizedPage } from "../../pages/index.ts";
import { MerchantDashboard, PaymentPage } from "../../pages/index.ts";
import ErrorsRouting from "./ErrorsRouting.tsx";

const RoleBasedRoutes = () => {
  // Simplified public routes without authentication
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/merchants" element={<MerchantsPage />} />
      <Route path="/payment" element={<PaymentPage />} />
      <Route path="/dashboard" element={<MerchantDashboard />} />
      {/* Error Routes */}
      <Route path="/unauthorized" element={<UnauthorizedPage />} />
      <Route path="/error/*" element={<ErrorsRouting />} />
      <Route path="*" element={<Navigate to="/error/404" />} />
    </Routes>
  );
};

export default RoleBasedRoutes;
