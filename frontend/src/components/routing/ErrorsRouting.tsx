import { Routes, Route, useParams, Link } from "react-router-dom";

const ErrorPage = () => {
  const { errorCode } = useParams();

  const getErrorMessage = (code: string = "404") => {
    switch (code) {
      case "404":
        return {
          title: "Page Not Found",
          message: "The page you are looking for does not exist.",
          suggestion: "Check the URL or return to the home page.",
        };
      case "403":
        return {
          title: "Access Forbidden",
          message: "You do not have permission to access this resource.",
          suggestion: "Please log in or contact support if you believe this is an error.",
        };
      case "500":
        return {
          title: "Internal Server Error",
          message: "Something went wrong on our end.",
          suggestion: "Please try again later or contact support if the problem persists.",
        };
      default:
        return {
          title: "An Error Occurred",
          message: "Something unexpected happened.",
          suggestion: "Please try again or return to the home page.",
        };
    }
  };

  const error = getErrorMessage(errorCode);

  return (
    <div className="min-h-screen flex items-center justify-center px-6 bg-slate-50">
      <div className="bg-white p-12 rounded-2xl shadow-xl text-center max-w-lg w-full">
        <div className="text-8xl font-bold text-red-500 mb-6">{errorCode || "404"}</div>

        <h1 className="text-3xl font-bold text-slate-800 mb-4">{error.title}</h1>

        <p className="text-lg text-slate-600 mb-4">{error.message}</p>

        <p className="text-slate-500 mb-10">{error.suggestion}</p>

        <div className="flex gap-4 justify-center flex-wrap">
          <Link to="/">
            <button className="px-6 py-3 bg-slate-900 text-white rounded-lg hover:bg-slate-800 transition-colors font-medium">Go Home</button>
          </Link>

          <button
            onClick={() => window.history.back()}
            className="px-6 py-3 bg-slate-500 text-white rounded-lg hover:bg-slate-600 transition-colors font-medium"
          >
            Go Back
          </button>
        </div>

        <div className="mt-10 pt-6 border-t border-slate-200">
          <p className="text-sm text-slate-400">
            Need help?{" "}
            <Link to="/onboarding" className="text-slate-600 hover:text-slate-800 underline">
              Try Onboarding
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

const ErrorsRouting = () => {
  return (
    <Routes>
      <Route path=":errorCode" element={<ErrorPage />} />
      <Route path="*" element={<ErrorPage />} />
    </Routes>
  );
};

export default ErrorsRouting;
