import { type ReactNode, useState } from "react";
import { SidebarNavigation } from "./SidebarNavigation";

interface NavSection {
  id: string;
  title: string;
  subsections?: { id: string; title: string }[];
}

interface PageLayoutProps {
  title: string;
  subtitle: string;
  children: ReactNode;
  sections: NavSection[];
  gradientFrom?: string;
  gradientTo?: string;
}

export const PageLayout = ({
  title,
  subtitle,
  children,
  sections,
  gradientFrom = "from-slate-800",
  gradientTo = "to-slate-900",
}: PageLayoutProps) => {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const closeMobileSidebar = () => {
    setIsMobileSidebarOpen(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-50/30">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Sidebar Navigation - Fixed positioned on desktop */}
        <div className="hidden lg:block">
          <SidebarNavigation sections={sections} />
        </div>

        {/* Main Content */}
        <div className="lg:ml-72">
          {/* Header Card */}
          <div className="bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden mb-8">
            <div className={`bg-gradient-to-r ${gradientFrom} ${gradientTo} px-8 py-8`}>
              <h1 className="text-3xl font-bold text-white mb-2">{title}</h1>
              <p className="text-white/90 text-lg">{subtitle}</p>
            </div>
          </div>

          {/* Content Area */}
          <div className="space-y-8">{children}</div>
        </div>

        {/* Mobile Navigation - Floating Button */}
        <div className="lg:hidden fixed bottom-6 right-6 z-50">
          <button
            onClick={toggleMobileSidebar}
            className="bg-slate-800 hover:bg-slate-900 text-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-105"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Sidebar Overlay */}
        <div
          className={`lg:hidden fixed inset-0 z-40 transition-opacity duration-300 ${
            isMobileSidebarOpen ? "opacity-100" : "opacity-0 pointer-events-none"
          }`}
        >
          <div className="absolute inset-0 bg-black/50 transition-opacity duration-300" onClick={closeMobileSidebar} />
          <div
            className={`absolute top-0 left-0 h-full w-80 max-w-[80vw] bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${
              isMobileSidebarOpen ? "translate-x-0" : "-translate-x-full"
            }`}
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-slate-800">Navigation</h3>
                <button onClick={closeMobileSidebar} className="text-slate-400 hover:text-slate-600 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <SidebarNavigation sections={sections} className="relative" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
