// ===================================================================
// ADMIN REGISTRATION TEMPORARILY DISABLED
// ===================================================================
// This entire admin registration component has been commented out
// because the database backend is temporarily disabled.
//
// To re-enable: Uncomment all the code below
// ===================================================================

/*
import { useState } from "react";
import { createAdmin, type CreateAdminRequest } from "../services/api";
import { useAppDispatch } from "../redux";
import { signIn } from "../redux/slices/authSlice";
import LoadingSpinner from "./LoadingSpinner";

interface AdminRegistrationProps {
  onBack: () => void;
  onSuccess: () => void;
}

export const AdminRegistration = ({ onBack, onSuccess }: AdminRegistrationProps) => {
  const dispatch = useAppDispatch();
  const [formData, setFormData] = useState<CreateAdminRequest>({
    email: "",
    password: "",
    firstName: "",
    lastName: "",
    inviteCode: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      const response = await createAdmin(formData);

      if (response.success && response.data) {
        dispatch(
          signIn({
            user: {
              id: response.data.userId,
              email: response.data.email,
              name: response.data.name,
            },
            token: response.data.token,
          })
        );

        onSuccess();
      } else {
        setError(response.message || "Registration failed");
      }
    } catch {
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const isValidForm = () => {
    return formData.email.trim() && formData.password.trim() && formData.firstName.trim() && formData.lastName.trim() && formData.inviteCode.trim();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Create Platform Admin</h2>
        <p className="text-slate-600">Register as a platform administrator with a valid invite code</p>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-slate-700 mb-1">
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
              placeholder="Enter your first name"
            />
          </div>
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-slate-700 mb-1">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
              placeholder="Enter your last name"
            />
          </div>
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-1">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
            placeholder="Enter your email address"
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-slate-700 mb-1">
            Password
          </label>
          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 pr-10 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
              placeholder="Enter your password"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 flex items-center px-3 text-slate-400 hover:text-slate-600"
            >
              {showPassword ? "Hide Password" : "Show Password"}
            </button>
          </div>
          <p className="text-xs text-slate-500 mt-1">Must be at least 8 characters with uppercase, lowercase, number, and special character</p>
        </div>

        <div>
          <label htmlFor="inviteCode" className="block text-sm font-medium text-slate-700 mb-1">
            Invite Code
          </label>
          <input
            type="password"
            id="inviteCode"
            name="inviteCode"
            value={formData.inviteCode}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
            placeholder="Enter the admin invite code"
          />
        </div>

        <div className="flex space-x-4">
          <button
            type="button"
            onClick={onBack}
            disabled={isLoading}
            className="flex-1 px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Back to Login
          </button>

          <button
            type="submit"
            disabled={!isValidForm() || isLoading}
            className="flex-1 px-4 py-2 bg-slate-900 text-white rounded-lg hover:bg-slate-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
          >
            {isLoading ? <LoadingSpinner /> : "Create Admin Account"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdminRegistration;
*/

// Mock AdminRegistration component for when authentication is disabled
interface AdminRegistrationProps {
  onBack: () => void;
  onSuccess: () => void;
}

export const AdminRegistration = ({ onBack }: AdminRegistrationProps) => {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Admin Registration Disabled</h2>
        <p className="text-slate-600">Database functionality is temporarily disabled</p>
      </div>

      <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-amber-900 mb-1">Notice</h3>
            <div className="text-sm text-amber-800 space-y-1">
              <p>• Admin registration is temporarily disabled</p>
              <p>• Database backend services are not available</p>
              <p>• User management features are disabled</p>
            </div>
          </div>
        </div>
      </div>

      <button
        type="button"
        onClick={onBack}
        className="w-full px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
      >
        Back to Login
      </button>
    </div>
  );
};

export default AdminRegistration;
