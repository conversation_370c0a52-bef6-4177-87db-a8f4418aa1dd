import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { PayrixService } from "../../service/payrix.service.js";

interface TokenPaymentRequest {
  merchantId: string;
  token: string;
  amount: number;
  description?: string;
  customerInfo?: {
    email?: string;
    name?: string;
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
  };
}

interface PayrixTransactionResponse {
  success: boolean;
  transaction?: any;
  error?: string;
  message?: string;
}

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info("Process token payment request", {
      body: event.body,
      headers: event.headers,
    });

    if (!event.body) {
      return createResponse(400, {
        error: "Request body is required",
        message: "Please provide merchantId, token, and amount",
      });
    }

    const { merchantId, token, amount, description, customerInfo }: TokenPaymentRequest = JSON.parse(event.body);

    // Validate required fields
    if (!merchantId || !token || !amount) {
      return createResponse(400, {
        error: "Missing required fields",
        message: "merchantId, token, and amount are required",
      });
    }

    // Validate merchantId format
    if (typeof merchantId !== "string" || merchantId.trim().length === 0) {
      return createResponse(400, {
        error: "Invalid merchantId",
        message: "merchantId must be a non-empty string",
      });
    }

    // Validate token format
    if (typeof token !== "string" || token.trim().length === 0) {
      return createResponse(400, {
        error: "Invalid token",
        message: "token must be a non-empty string",
      });
    }

    // Validate amount
    if (typeof amount !== "number" || amount <= 0) {
      return createResponse(400, {
        error: "Invalid amount",
        message: "amount must be a positive number in cents",
      });
    }

    logger.info("Validating merchant and processing token payment", {
      merchantId,
      token: token.substring(0, 8) + "...", // Log only first 8 characters for security
      amount,
      description,
    });

    // Validate merchant with Payrix API
    const payrixService = new PayrixService();
    const validation = await payrixService.validateMerchantById(merchantId);

    if (!validation.isValid) {
      logger.warn("Merchant validation failed", {
        merchantId,
        error: validation.error,
      });

      return createResponse(404, {
        error: "Merchant validation failed",
        message: validation.error || "Invalid or inactive merchant",
        details: {
          merchantId,
          validationError: validation.error,
        },
      });
    }

    logger.info("Merchant validation successful, processing payment with token", {
      merchantId,
      merchantName: validation.merchant?.name,
      amount,
    });

    // Process payment using token
    const paymentResult = await payrixService.processTokenPayment({
      merchantId,
      token,
      amount,
      description: description || "Token-based payment",
      customerInfo,
    });

    if (!paymentResult.success) {
      logger.error("Token payment processing failed", {
        merchantId,
        token: token.substring(0, 8) + "...",
        error: paymentResult.error,
      });

      return createResponse(400, {
        error: "Payment processing failed",
        message: paymentResult.error || "Failed to process payment with token",
        details: {
          merchantId,
          amount,
        },
      });
    }

    logger.info("Token payment processed successfully", {
      merchantId,
      transactionId: paymentResult.transaction?.id,
      amount,
      status: paymentResult.transaction?.status,
    });

    // Attempt to clean up the token after successful payment
    try {
      await payrixService.deleteToken(token);
      logger.info("Token cleanup successful", {
        token: token.substring(0, 8) + "...",
        transactionId: paymentResult.transaction?.id,
      });
    } catch (cleanupError) {
      // Log cleanup failure but don't fail the payment response
      logger.warn("Token cleanup failed (payment still successful)", {
        token: token.substring(0, 8) + "...",
        transactionId: paymentResult.transaction?.id,
        cleanupError: cleanupError instanceof Error ? cleanupError.message : "Unknown cleanup error",
      });
    }

    return createResponse(200, {
      success: true,
      message: "Payment processed successfully",
      transaction: {
        id: paymentResult.transaction?.id,
        status: paymentResult.transaction?.status,
        amount: paymentResult.transaction?.total,
        merchantId,
        description: paymentResult.transaction?.description,
        createdAt: paymentResult.transaction?.created,
      },
      merchantInfo: {
        id: validation.merchant?.id,
        name: validation.merchant?.name,
        status: validation.merchant?.status,
      },
    });
  } catch (error) {
    logger.error("Error processing token payment", { error });

    // If we have token information, attempt cleanup on error
    try {
      const body = event.body ? JSON.parse(event.body) : {};
      if (body.token) {
        const payrixService = new PayrixService();
        await payrixService.deleteToken(body.token);
        logger.info("Token cleanup completed after error", {
          token: body.token.substring(0, 8) + "...",
        });
      }
    } catch (cleanupError) {
      logger.warn("Token cleanup failed after error", {
        cleanupError: cleanupError instanceof Error ? cleanupError.message : "Unknown cleanup error",
      });
    }

    return createResponse(500, {
      error: "Internal server error",
      message: "Failed to process token payment",
    });
  }
};
