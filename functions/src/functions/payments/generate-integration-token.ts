import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { randomBytes } from "crypto";
import { z } from "zod";
import { createIframeResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import {
  sanitizeInput,
  validateAmount,
  validateDescription,
  validateMerchantIdFormat,
  validateReturnUrl,
  withIframeSecurity,
} from "../../middleware/security.js";
import { PayrixService } from "../../service/payrix.service.js";
import { setToken, validateToken as validateTokenData, markTokenAsUsed as markTokenUsedInStorage } from "../../service/token-storage.js";

interface IntegrationTokenRequest {
  merchantId: string;
  description: string;
  amount?: number;
  returnUrl?: string;
  expiresIn?: number; // minutes, default 60
  // E-commerce compliance fields
  currency?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
    commodityCode?: string;
    productCode?: string;
  }>;
  taxAmount?: number;
  shippingAmount?: number;
  dutyAmount?: number;
  orderNumber?: string;
  invoiceNumber?: string;
  customerCode?: string;
  orderDiscount?: number;
}

interface IntegrationTokenResponse {
  token: string;
  expiresAt: string;
  embedUrl: string;
  merchantInfo: {
    id: string;
    name: string;
    status: number;
  };
}

// Item schema for e-commerce compliance
const itemSchema = z.object({
  name: z.string().min(1, "Item name is required"),
  description: z.string().optional(),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  unitPrice: z.number().int().min(0, "Unit price must be non-negative"),
  total: z.number().int().min(0, "Total must be non-negative"),
  commodityCode: z.string().optional(),
  productCode: z.string().optional(),
});

// Validation schema
const tokenRequestSchema = z.object({
  merchantId: z.string().min(1, "Merchant ID is required"),
  description: z.string().min(1, "Description is required"),
  amount: z.number().int().min(1, "Amount must be at least 1 cent").optional(),
  returnUrl: z.string().url("Return URL must be a valid URL").optional(),
  expiresIn: z.number().int().min(1).max(1440, "Expires in must be between 1 and 1440 minutes").optional().default(60),
  // E-commerce compliance fields
  currency: z.string().length(3, "Currency must be 3-letter ISO code").optional().default("USD"),
  items: z.array(itemSchema).optional(),
  taxAmount: z.number().int().min(0, "Tax amount must be non-negative").optional(),
  shippingAmount: z.number().int().min(0, "Shipping amount must be non-negative").optional(),
  dutyAmount: z.number().int().min(0, "Duty amount must be non-negative").optional(),
  orderNumber: z.string().optional(),
  invoiceNumber: z.string().optional(),
  customerCode: z.string().optional(),
  orderDiscount: z.number().int().min(0, "Discount must be non-negative").optional(),
});

function generateSecureToken(): string {
  return randomBytes(32).toString("hex");
}

// Export function to validate and consume tokens (for iframe endpoint)
export const validateToken = async (
  token: string
): Promise<{
  isValid: boolean;
  data?: {
    merchantId: string;
    description: string;
    amount: number;
    returnUrl?: string;
    // E-commerce compliance fields
    currency?: string;
    items?: Array<{
      name: string;
      description?: string;
      quantity: number;
      unitPrice: number;
      total: number;
      commodityCode?: string;
      productCode?: string;
    }>;
    taxAmount?: number;
    shippingAmount?: number;
    dutyAmount?: number;
    orderNumber?: string;
    invoiceNumber?: string;
    customerCode?: string;
    orderDiscount?: number;
    // Merchant information
    merchantInfo?: {
      address?: {
        line1?: string;
        line2?: string;
        city?: string;
        state?: string;
        zip?: string;
        country?: string;
      };
      contactEmail?: string;
      contactPhone?: string;
    };
  };
  error?: string;
}> => {
  return await validateTokenData(token);
};

// Export function to mark token as used
export const markTokenAsUsed = async (token: string): Promise<boolean> => {
  return await markTokenUsedInStorage(token);
};

const handlerImpl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info("Generate integration token request", {
      body: event.body,
      headers: event.headers,
    });

    // DynamoDB TTL handles automatic cleanup of expired tokens

    if (!event.body) {
      return createIframeResponse(400, {
        error: "Request body is required",
        message: "Please provide merchantId and description",
      });
    }

    // Parse and validate request
    let requestData: IntegrationTokenRequest;
    try {
      const parsedBody = JSON.parse(event.body);
      requestData = tokenRequestSchema.parse(parsedBody);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createIframeResponse(400, {
          error: "Validation failed",
          message: "Invalid request data",
          details: error.errors,
        });
      }
      throw error;
    }

    const {
      merchantId,
      description,
      amount = 1000,
      returnUrl,
      expiresIn = 60, // default 60 minutes
      currency = "USD",
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
    } = requestData;

    // Security validations
    const merchantValidation = validateMerchantIdFormat(merchantId);
    if (!merchantValidation.isValid) {
      return createIframeResponse(merchantValidation.statusCode || 400, {
        error: merchantValidation.error,
        message: "Invalid merchant ID",
      });
    }

    const descriptionValidation = validateDescription(description);
    if (!descriptionValidation.isValid) {
      return createIframeResponse(descriptionValidation.statusCode || 400, {
        error: descriptionValidation.error,
        message: "Invalid description",
      });
    }

    const amountValidation = validateAmount(amount);
    if (!amountValidation.isValid) {
      return createIframeResponse(amountValidation.statusCode || 400, {
        error: amountValidation.error,
        message: "Invalid amount",
      });
    }

    if (returnUrl) {
      const urlValidation = validateReturnUrl(returnUrl);
      if (!urlValidation.isValid) {
        return createIframeResponse(urlValidation.statusCode || 400, {
          error: urlValidation.error,
          message: "Invalid return URL",
        });
      }
    }

    // Validate tax amount if provided (must be 0.1%-22% of total for Visa, 0.1%-30% for MasterCard)
    if (taxAmount !== undefined && taxAmount > 0) {
      const taxPercentage = (taxAmount / amount) * 100;
      if (taxPercentage < 0.1 || taxPercentage > 30) {
        return createIframeResponse(400, {
          error: "Invalid tax amount",
          message: "Tax amount must be between 0.1% and 30% of the total amount",
          details: {
            taxAmount,
            amount,
            percentage: taxPercentage.toFixed(2),
          },
        });
      }
    }

    // Validate order/invoice/customer code for Level 2/3 compliance
    if (items && items.length > 0) {
      // For Level 2/3, order number or invoice number is required
      if (!orderNumber && !invoiceNumber && !customerCode) {
        return createIframeResponse(400, {
          error: "Missing required field",
          message: "Order number, invoice number, or customer code is required for itemized transactions",
        });
      }
    }

    // Sanitize inputs
    const sanitizedDescription = sanitizeInput(description);

    logger.info("Processing integration token request", {
      merchantId,
      description: sanitizedDescription,
      amount,
      returnUrl,
      expiresIn,
    });

    // Validate merchant with Payrix API
    const payrixService = new PayrixService();
    const validation = await payrixService.validateMerchantById(merchantId);

    if (!validation.isValid) {
      logger.warn("Merchant validation failed", {
        merchantId,
        error: validation.error,
      });

      return createIframeResponse(404, {
        error: "Merchant validation failed",
        message: validation.error || "Invalid or inactive merchant",
        details: {
          merchantId,
          validationError: validation.error,
        },
      });
    }

    logger.info("Merchant validation successful, generating integration token", {
      merchantId,
      merchantName: validation.merchant?.dba || validation.merchant?.name,
    });

    // Generate secure token
    const token = generateSecureToken();

    const expiresAt = new Date(Date.now() + expiresIn * 60 * 1000); // Convert minutes to milliseconds

    // Store token data with all e-commerce fields
    await setToken(token, {
      merchantId,
      description: sanitizedDescription,
      amount,
      returnUrl,
      expiresAt,
      used: false,
      // E-commerce compliance fields
      currency,
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
      // Merchant information
      merchantInfo: {
        address: validation.merchant?.address
          ? {
              line1: validation.merchant.address.line1,
              line2: validation.merchant.address.line2,
              city: validation.merchant.address.city,
              state: validation.merchant.address.state,
              zip: validation.merchant.address.zip,
              country: validation.merchant.address.country || "US",
            }
          : undefined,
        contactEmail: validation.merchant?.email,
        contactPhone: validation.merchant?.phone,
      },
    });

    // Generate embed URL
    const baseUrl = process.env.FRONTEND_URL || "https://your-domain.com";
    const embedUrl = `${baseUrl}/payment-iframe?token=${token}`;

    const response: IntegrationTokenResponse = {
      token,
      expiresAt: expiresAt.toISOString(),
      embedUrl,
      merchantInfo: {
        id: merchantId,
        name: validation.merchant?.dba || validation.merchant?.name || "Unknown",
        status: validation.merchant?.status || 0,
      },
    };

    logger.info("Integration token generated successfully", {
      token: token.substring(0, 8) + "...",
      expiresAt: expiresAt.toISOString(),
      merchantId,
    });

    return createIframeResponse(200, {
      success: true,
      message: "Integration token generated successfully",
      data: response,
    });
  } catch (error) {
    logger.error("Error generating integration token", { error });

    return createIframeResponse(500, {
      error: "Internal server error",
      message: "Failed to generate integration token",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Export the handler wrapped with security middleware
export const handler = withIframeSecurity(handlerImpl);
