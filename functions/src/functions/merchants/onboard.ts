// ===================================================================
// MERCHANTS ONBOARD LAMBDA DATABASE FUNCTIONALITY TEMPORARILY DISABLED
// ===================================================================
// Database operations have been commented out to disable database
// functionality. To re-enable: uncomment database-related code below
// ===================================================================

import { APIGatewayProxyHandler } from "aws-lambda";
// import { randomUUID } from "crypto";
// import { AppDataSource } from "../../data-source/index.js";
// import { Merchant } from "../../entity/merchant.entity.js";
// import { MerchantBankAccount } from "../../entity/merchant-bank-account.entity.js";
// import { MerchantMember } from "../../entity/merchant-member.entity.js";
// import { MerchantAuditLog } from "../../entity/merchant-audit-log.entity.js";
// import { User, UserRole, UserStatus, UserType } from "../../entity/user.entity.js";
import { PayrixService } from "../../service/payrix.service.js";
import { validateOnboardingRequest } from "./schemas/onboarding.schema.js";
import { createResponse, createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
// import * as bcrypt from "bcrypt";

/*
const createMerchant = async (merchantId: string, data: OnboardingRequest): Promise<Merchant> => {
  const merchant = new Merchant();
  merchant.merchant_id = merchantId;
  merchant.legal_name = data.name?.trim() || "";
  merchant.business_name = data.merchant?.dba?.trim() || "";
  merchant.email = data.email?.trim() || "";
  merchant.phone = data.phone?.trim() || "";
  merchant.website = data.website?.trim() || "";
  merchant.ein = data.ein?.trim() || "";
  merchant.address1 = data.address1?.trim() || "";
  merchant.address2 = data.address2?.trim() || "";
  merchant.city = data.city?.trim() || "";
  merchant.state = data.state?.trim() || "";
  merchant.zip = data.zip?.trim() || "";
  merchant.country = data.country?.trim() || "USA";
  merchant.currency = data.currency?.trim() || "USD";
  merchant.dba_name = data.merchant?.dba?.trim() || "";
  merchant.mcc = data.merchant?.mcc?.trim() || "";
  merchant.tc_version = data.tcVersion?.trim() || "1.0";

  if (data.tcDate) {
    // Check if date is in format YYYYMMDDHHMM (12 digits)
    if (/^\d{12}$/.test(data.tcDate)) {
      // Parse YYYYMMDDHHMM format into Date object
      const year = parseInt(data.tcDate.substring(0, 4)); // YYYY
      const month = parseInt(data.tcDate.substring(4, 6)) - 1; // MM (0-based)
      const day = parseInt(data.tcDate.substring(6, 8)); // DD
      const hour = parseInt(data.tcDate.substring(8, 10)); // HH
      const minute = parseInt(data.tcDate.substring(10, 12)); // MM
      merchant.tc_acceptance_date = new Date(year, month, day, hour, minute);
    } else {
      // Handle other date formats
      merchant.tc_acceptance_date = new Date(data.tcDate);
    }
  } else {
    // Default to current date if no date provided
    merchant.tc_acceptance_date = new Date();
  }

  merchant.tc_ip_address = data.tcAcceptanceIp?.trim() || data.clientIp?.trim() || null;
  merchant.status = 1;
  merchant.verification_status = 0;
  merchant.entity_type = data.type === undefined || data.type === null ? 2 : Number(data.type);
  merchant.is_new = data.merchant?.new === 1;
  merchant.annual_cc_sales =
    data.merchant?.annualCCSales === undefined || data.merchant?.annualCCSales === null ? 0 : Number(data.merchant.annualCCSales);
  merchant.avg_ticket = data.merchant?.avgTicket === undefined || data.merchant?.avgTicket === null ? 0 : Number(data.merchant.avgTicket);
  // Handle established date in YYYYMMDD format
  if (data.merchant?.established) {
    const establishedStr = data.merchant.established.replace(/\D/g, "");
    if (/^\d{8}$/.test(establishedStr)) {
      // Parse YYYYMMDD format
      const year = parseInt(establishedStr.substring(0, 4));
      const month = parseInt(establishedStr.substring(4, 6)) - 1; // 0-based month
      const day = parseInt(establishedStr.substring(6, 8));
      merchant.established_date = new Date(year, month, day);
    } else {
      merchant.established_date = new Date(data.merchant.established);
    }
  } else {
    merchant.established_date = new Date();
  }

  return await AppDataSource.getRepository(Merchant).save(merchant);
};
*/

/*
const createBankAccount = async (merchantId: string, data: OnboardingRequest): Promise<MerchantBankAccount> => {
  const account = data.accounts[0];
  const bankAccount = new MerchantBankAccount();
  bankAccount.account_id = randomUUID();
  bankAccount.merchant_id = merchantId;
  bankAccount.account_type = "all";
  bankAccount.is_primary = account.primary === 1;
  bankAccount.account_method = Number(account.account.method) || 1;
  bankAccount.account_number = account.account.number;
  bankAccount.routing_number = account.account.routing;
  bankAccount.currency = data.currency?.trim() || "USD";
  bankAccount.status = 0;

  return await AppDataSource.getRepository(MerchantBankAccount).save(bankAccount);
};

const createMembers = async (merchantId: string, data: OnboardingRequest): Promise<MerchantMember[]> => {
  const memberRepo = AppDataSource.getRepository(MerchantMember);
  const members: MerchantMember[] = [];

  for (const memberData of data.merchant?.members || []) {
    const member = new MerchantMember();
    member.member_id = randomUUID();
    member.merchant_id = merchantId;
    member.title = memberData.title?.trim() || "";
    member.first_name = memberData.first?.trim() || "";
    member.last_name = memberData.last?.trim() || "";
    member.ssn = memberData.ssn?.trim() || "";

    if (memberData.dob) {
      if (/^\d{8}$/.test(memberData.dob.replace(/\D/g, ""))) {
        const dobStr = memberData.dob.replace(/\D/g, "");
        // Parse YYYYMMDD format (as required by Payrix)
        const year = parseInt(dobStr.substring(0, 4)); // YYYY
        const month = parseInt(dobStr.substring(4, 6)) - 1; // MM (0-based)
        const day = parseInt(dobStr.substring(6, 8)); // DD
        member.date_of_birth = new Date(year, month, day);
      } else {
        member.date_of_birth = new Date(memberData.dob);
      }
    }

    member.ownership_percentage = memberData.ownership === undefined || memberData.ownership === null ? 0 : Number(memberData.ownership) / 100;
    member.significant_responsibility = memberData.significantResponsibility === 1;
    member.politically_exposed = memberData.politicallyExposed === 1;
    member.email = memberData.email?.trim() || "";
    member.phone = memberData.phone?.trim() || "";
    member.is_primary = memberData.primary === "1";
    member.address1 = memberData.address1?.trim() || "";
    member.address2 = memberData.address2?.trim() || "";
    member.city = memberData.city?.trim() || "";
    member.state = memberData.state?.trim() || "";
    member.zip = memberData.zip?.trim() || "";
    member.country = memberData.country?.trim() || "USA";

    members.push(await memberRepo.save(member));
  }

  return members;
};
*/

/*
const createUser = async (merchantId: string, data: OnboardingRequest): Promise<User> => {
  const primaryMember = data.merchant.members.find((member) => member.primary === "1") || data.merchant.members[0];

  if (!primaryMember) {
    throw new Error("No primary member found to create user account");
  }

  const tempPassword = Math.random().toString(36).slice(-12) + "A1!";
  const hashedPassword = await bcrypt.hash(tempPassword, 12);

  const user = new User();
  user.user_id = randomUUID();
  user.merchant_id = merchantId;
  user.email = primaryMember.email;
  user.password_hash = hashedPassword;
  user.first_name = primaryMember.first;
  user.last_name = primaryMember.last;
  user.title = primaryMember.title || "Business Owner";
  user.user_type = UserType.MERCHANT_USER;
  user.role = UserRole.MERCHANT_ADMIN;
  user.status = UserStatus.PENDING_VERIFICATION;
  user.email_verified = false;
  user.email_verification_token = randomUUID();
  user.created_by = "system";
  user.notification_preferences = user.getDefaultNotificationPreferences();

  const savedUser = await AppDataSource.getRepository(User).save(user);

  logger.info("User account created for merchant", {
    merchantId,
    userId: savedUser.user_id,
    email: savedUser.email,
    tempPassword,
  });

  return savedUser;
};

const createAuditLog = async (merchantId: string, action: string, details: string): Promise<void> => {
  const auditLog = new MerchantAuditLog();
  auditLog.log_id = randomUUID();
  auditLog.merchant_id = merchantId;
  auditLog.table_name = "merchants";
  auditLog.record_id = merchantId;
  auditLog.action = action;
  auditLog.changed_by = "system";
  auditLog.changes = JSON.stringify({ details });

  await AppDataSource.getRepository(MerchantAuditLog).save(auditLog);
};
*/
export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;
  const clientIp = event.requestContext.identity?.sourceIp || event.headers["X-Forwarded-For"] || event.headers["x-forwarded-for"] || "unknown";

  try {
    logger.info("Merchant onboarding started - Direct Payrix Integration", { requestId, clientIp });

    // 1. Parse and validate request body
    const body = JSON.parse(event.body || "{}");
    const validation = validateOnboardingRequest(body);

    if (!validation.success || !validation.data) {
      logger.warn("Onboarding validation failed", {
        requestId,
        clientIp,
        errors: validation.errors || ["Unknown validation error"],
      });
      return createResponse(400, {
        success: false,
        message: "Validation failed",
        errors: validation.errors || ["Unknown validation error"],
      });
    }

    const data = validation.data;

    // 2. Generate merchant ID for tracking (no database storage)
    // const merchantId = randomUUID();

    logger.info("Processing direct Payrix submission", {
      requestId,
      // merchantId,
      email: data.email,
      legalName: data.name,
    });

    // 3. Check if merchant already exists in Payrix
    const payrixService = new PayrixService();

    try {
      logger.info("Checking for existing merchant in Payrix", {
        requestId,
        email: data.email,
      });

      const merchantExists = await payrixService.checkMerchantExists(data.email, data.ein);

      if (merchantExists) {
        logger.warn("Merchant already exists in Payrix", {
          requestId,
          email: data.email,
          ein: data.ein ? "[REDACTED]" : undefined,
        });

        return createResponse(409, {
          success: false,
          message:
            "A merchant with this email address already exists in our system. Please use a different email address or contact support if you believe this is an error.",
          error: "MERCHANT_ALREADY_EXISTS",
          details: {
            email: data.email,
            conflictType: "duplicate_merchant",
          },
        });
      }

      logger.info("No existing merchant found, proceeding with onboarding", {
        requestId,
        email: data.email,
      });
    } catch (duplicateCheckError) {
      logger.error("Error during duplicate merchant check", {
        requestId,
        error: (duplicateCheckError as Error).message,
        stack: (duplicateCheckError as Error).stack,
      });

      return createResponse(500, {
        success: false,
        message: "Unable to verify merchant uniqueness. Please try again later.",
        error: "DUPLICATE_CHECK_FAILED",
        details: {
          reason: "Service temporarily unavailable",
        },
      });
    }

    // 4. Direct Payrix integration - no database storage
    let payrixResponse: { id: string; [key: string]: unknown };
    let payrixEntityId: string;
    let userAccountData: { id: string; email: string; [key: string]: unknown } | null = null;

    try {
      logger.info("Creating merchant in Payrix (direct integration)", { requestId });

      // Send merchant data directly to Payrix
      payrixResponse = await payrixService.createMerchant(data);
      payrixEntityId = payrixResponse?.id;

      if (!payrixEntityId) {
        throw new Error("Payrix response did not contain entity ID");
      }

      logger.info("Payrix merchant created successfully (direct integration)", {
        requestId,
        payrixEntityId,
      });

      // 5. Create user account if requested
      if (data.createAccount && data.username && data.password) {
        try {
          logger.info("Creating user account for merchant", {
            requestId,
            payrixEntityId,
            username: data.username,
          });

          const primaryMember = data.merchant.members.find((member) => member.primary === "1") || data.merchant.members[0];

          if (!primaryMember) {
            throw new Error("No primary member found to create user account");
          }

          userAccountData = await payrixService.createUserAccount({
            username: data.username,
            password: data.password,
            first: primaryMember.first,
            last: primaryMember.last,
            email: primaryMember.email,
            merchantId: payrixEntityId,
          });

          logger.info("User account created successfully", {
            requestId,
            payrixEntityId,
            username: data.username,
            userAccountId: userAccountData?.id,
          });
        } catch (userError) {
          logger.error("User account creation failed", {
            requestId,
            payrixEntityId,
            username: data.username,
            error: (userError as Error).message,
          });

          // Don't fail the entire onboarding if user account creation fails
          // Just log the error and continue
        }
      }

      // Success response with Payrix data
      return createResponse(201, {
        success: true,
        payrixEntityId,
        message: "Merchant onboarding completed successfully via direct Payrix integration",
        data: {
          merchant: {
            legal_name: data.name,
            email: data.email,
            verification_status: 1,
          },
          payrixResponse,
          userAccount: userAccountData
            ? {
                id: userAccountData.id,
                username: userAccountData.sanitizedUsername || data.username,
                originalUsername: data.username,
                email: userAccountData.email,
                created: true,
              }
            : null,
        },
      });
    } catch (payrixError) {
      logger.error("Payrix API error (direct integration)", {
        requestId,
        // merchantId,
        error: (payrixError as Error).message,
        stack: (payrixError as Error).stack,
      });

      return createResponse(422, {
        success: false,
        // merchantId,
        message: "Payrix onboarding failed. Please check your information and try again.",
        error: (payrixError as Error).message,
      });
    }

    /*
    const data = validation.data;

    // 2. Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      logger.info("Database connection initialized", { requestId });
    }

    // 3. Generate merchant ID and create local records
    const merchantId = randomUUID();
    createdMerchantId = merchantId;

    logger.info("Creating merchant records", {
      requestId,
      merchantId,
      email: data.email,
      legalName: data.name,
    });

    // 4. Database operations - Create merchant records
    const merchant = await createMerchant(merchantId, data);
    await createBankAccount(merchantId, data);
    await createMembers(merchantId, data);
    const user = await createUser(merchantId, data);

    logger.info("Local merchant records created", {
      requestId,
      merchantId,
      userId: user.user_id,
      email: user.email,
    });

    const payrixService = new PayrixService();
    let payrixResponse;
    let payrixEntityId;

    try {
      logger.info("Creating merchant in Payrix", { requestId, merchantId });

      // 5. Create merchant in Payrix system
      payrixResponse = await payrixService.createMerchant(data);
      payrixEntityId = payrixResponse?.id;

      if (payrixEntityId) {
        logger.info("Payrix merchant created successfully", {
          requestId,
          merchantId,
          payrixEntityId,
        });

        // 6. Database operation - Update merchant with Payrix ID
        await AppDataSource.getRepository(Merchant).update(merchantId, {
          external_entity_id: payrixEntityId,
          verification_status: 1,
        });
      }
    } catch (payrixError) {
      logger.error("Payrix API error", {
        requestId,
        merchantId,
        error: (payrixError as Error).message,
        stack: (payrixError as Error).stack,
      });

      // 7. Database operation - Update merchant status on Payrix error
      await AppDataSource.getRepository(Merchant).update(merchantId, {
        verification_status: -1,
        regulatory_notes: `Payrix API Error: ${(payrixError as Error).message}`,
      });

      return createResponse(422, {
        success: false,
        merchantId,
        message: "Merchant saved locally but Payrix onboarding failed. Please contact support.",
        error: (payrixError as Error).message,
      });
    }

    // 8. Database operation - Create audit log
    await createAuditLog(
      merchantId,
      "ONBOARDED",
      `Successfully onboarded merchant with Payrix entity ID: ${payrixEntityId}. Created user account: ${user.email}`
    );

    logger.info("Merchant onboarding completed successfully", {
      requestId,
      merchantId,
      payrixEntityId,
      userEmail: user.email,
      userId: user.user_id,
    });

    return createResponse(201, {
      success: true,
      merchantId,
      payrixEntityId,
      message: "Merchant onboarding completed successfully",
      data: {
        merchant: {
          merchant_id: merchantId,
          legal_name: merchant.legal_name,
          email: merchant.email,
          verification_status: 1,
        },
        user: {
          user_id: user.user_id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          title: user.title,
        },
        payrixResponse,
      },
    });
    */
  } catch (error) {
    const err = error as Error;
    logger.error("Error in onboard handler", {
      requestId,
      error: err.message,
      stack: err.stack,
    });

    return createErrorResponse(500, "Internal Server Error", err.message);
  }
};
