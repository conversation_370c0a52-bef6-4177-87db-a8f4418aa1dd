// ===================================================================
// MERCHANTS LOGIN LAMBDA DATABASE FUNCTIONALITY TEMPORARILY DISABLED
// ===================================================================
// Database operations have been commented out to disable database
// functionality. To re-enable: uncomment database-related code below
// ===================================================================

import { APIGatewayProxyHandler } from "aws-lambda";
import { z } from "zod";
// import { UserService } from "../../service/user.service.js";
// import { AppDataSource } from "../../data-source/index.js";
import { createResponse, createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";

const LoginSchema = z.object({
  email: z.string().email("Invalid email format").min(1, "Email is required"),
  password: z.string().min(1, "Password is required"),
});

export const handler: APIGatewayProxyHandler = async (event) => {
  try {
    // Database functionality is temporarily disabled
    logger.warn("⚠️  Database functionality is temporarily disabled");

    // Parse request body for validation
    const body = JSON.parse(event.body || "{}");
    const validation = LoginSchema.safeParse(body);

    if (!validation.success) {
      const errors = validation.error.errors.map((error) => error.message);
      logger.warn("Login validation failed", {
        errors: errors,
        requestId: event.requestContext.requestId,
      });
      return createErrorResponse(400, errors.join(", "));
    }

    // Return mock response indicating database is disabled
    return createErrorResponse(503, "Service temporarily unavailable - database functionality is disabled");

    /*
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      logger.info("Database connection initialized");
    }

    const { email, password } = validation.data;
    const userService = new UserService();

    // Get client IP for audit logging
    const clientIp = event.requestContext.identity?.sourceIp || event.headers["X-Forwarded-For"] || event.headers["x-forwarded-for"] || "unknown";

    logger.info("Login attempt", {
      email,
      clientIp,
      requestId: event.requestContext.requestId,
      userAgent: event.headers["User-Agent"] || event.headers["user-agent"],
    });

    // Authenticate user
    const authResult = await userService.authenticate(email, password, clientIp);

    if (!authResult) {
      logger.warn("Authentication failed", {
        email,
        clientIp,
        requestId: event.requestContext.requestId,
      });
      return createErrorResponse(401, "Invalid email or password");
    }

    const { user, token } = authResult;

    logger.info("Login successful", {
      userId: user.user_id,
      email: user.email,
      role: user.role,
      userType: user.user_type,
      clientIp,
      requestId: event.requestContext.requestId,
    });

    return createResponse(200, {
      success: true,
      message: "Login successful",
      data: {
        user: {
          id: user.user_id,
          email: user.email,
          name: user.full_name,
          role: user.role,
          userType: user.user_type,
        },
        token,
      },
    });
    */
  } catch (error) {
    logger.error("Login error", {
      error: (error as Error).message,
      stack: (error as Error).stack,
      requestId: event.requestContext.requestId,
    });

    return createErrorResponse(500, "Internal server error", (error as Error).message);
  }
};
