// ===================================================================
// MERCHANTS LIST LAMBDA DATABASE FUNCTIONALITY TEMPORARILY DISABLED
// ===================================================================
// Database operations have been commented out to disable database
// functionality. To re-enable: uncomment database-related code below
// ===================================================================

import type { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from "aws-lambda";
// import { AppDataSource } from "../../data-source/index.js";
// import { Merchant } from "../../entity/merchant.entity.js";
import { createResponse, createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";

const STATUS_MAP = {
  1: "Active",
  2: "Pending",
  3: "Suspended",
  4: "Rejected",
} as const;

const getPaginationParams = (event: APIGatewayProxyEvent) => {
  const page = Math.max(1, parseInt(event.queryStringParameters?.page || "1"));
  const limit = Math.min(100, Math.max(1, parseInt(event.queryStringParameters?.limit || "20")));
  const offset = (page - 1) * limit;

  return { page, limit, offset };
};
export const handler = async (event: APIGatewayProxyEvent, _context: Context): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;

  try {
    // Database functionality is temporarily disabled
    logger.warn("⚠️  Database functionality is temporarily disabled", { requestId });

    const { page, limit } = getPaginationParams(event);

    // Return mock response indicating database is disabled
    return createResponse(200, {
      message: "Service temporarily unavailable - database functionality is disabled",
      merchants: [],
      pagination: {
        page,
        limit,
        totalCount: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    });

    /*
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      logger.info("Database connection initialized", { requestId });
    }

    const { page, limit, offset } = getPaginationParams(event);

    logger.info("Merchant list request", {
      requestId,
      pagination: { page, limit, offset },
    });

    const merchantRepository = AppDataSource.getRepository(Merchant);

    // Query merchants with pagination and get total count
    const [merchants, totalCount] = await merchantRepository.findAndCount({
      order: { created_at: "DESC" },
      take: limit,
      skip: offset,
    });

    // Format merchant data for response
    const formattedMerchants = merchants.map((merchant) => ({
      merchant_id: merchant.merchant_id,
      legal_name: merchant.legal_name,
      doing_business_as: merchant.business_name,
      email: merchant.email,
      status: STATUS_MAP[merchant.status as keyof typeof STATUS_MAP] || "Unknown",
      created_at: merchant.created_at.toISOString(),
    }));

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);

    logger.info("Merchants listed successfully", {
      requestId,
      totalCount,
      returnedCount: formattedMerchants.length,
      pagination: { page, limit, totalPages },
    });

    return createResponse(200, {
      message: "Merchants listed successfully",
      merchants: formattedMerchants,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    });
    */
  } catch (error) {
    logger.error("Error listing merchants", {
      requestId,
      error: (error as Error).message,
      stack: (error as Error).stack,
    });
    return createErrorResponse(500, "Internal server error", (error as Error).message);
  }
};
