// ===================================================================
// CREATE ADMIN LAMBDA DATABASE FUNCTIONALITY TEMPORARILY DISABLED
// ===================================================================
// Database operations have been commented out to disable database
// functionality. To re-enable: uncomment database-related code below
// ===================================================================

import { APIGatewayProxyHandler } from "aws-lambda";
import { z } from "zod";
// import { UserService } from "../../service/user.service.js";
// import { User, UserRole, UserType } from "../../entity/user.entity.js";
// import { AppDataSource } from "../../data-source/index.js";
import { createResponse, createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";

const CreateAdminSchema = z.object({
  email: z.string().email("Invalid email format").min(1, "Email is required"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters long")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/\d/, "Password must contain at least one number")
    .regex(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character"),
  firstName: z.string().min(1, "First name is required").trim(),
  lastName: z.string().min(1, "Last name is required").trim(),
  inviteCode: z.string().min(1, "Invite code is required"),
});

// const ADMIN_INVITE_CODE = process.env.ADMIN_INVITE_CODE || "PLATFORM_ADMIN_2024";

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;
  const clientIp = event.requestContext.identity?.sourceIp || event.headers["X-Forwarded-For"] || event.headers["x-forwarded-for"] || "unknown";

  try {
    logger.info("Platform admin creation started", { requestId, clientIp });

    // Database functionality is temporarily disabled
    logger.warn("⚠️  Database functionality is temporarily disabled", { requestId });

    const body = JSON.parse(event.body || "{}");
    const validation = CreateAdminSchema.safeParse(body);

    if (!validation.success) {
      const errors = validation.error.errors.map((error) => error.message);
      logger.warn("Admin creation validation failed", {
        requestId,
        clientIp,
        errors,
      });
      return createResponse(400, {
        success: false,
        message: "Validation failed",
        errors,
      });
    }

    // Return mock response indicating database is disabled
    return createErrorResponse(503, "Service temporarily unavailable - database functionality is disabled");

    /*
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      logger.info("Database connection initialized", { requestId });
    }

    const data = validation.data;

    if (data.inviteCode !== ADMIN_INVITE_CODE) {
      logger.warn("Invalid invite code attempt", {
        requestId,
        clientIp,
        email: data.email,
      });
      return createErrorResponse(403, "Invalid invite code");
    }

    const userService = new UserService();

    // Check if user already exists in database
    const existingUser = await userService.findByEmail(data.email);
    if (existingUser) {
      logger.warn("Admin creation attempt for existing user", {
        requestId,
        clientIp,
        email: data.email,
        existingUserId: existingUser.user_id,
      });
      return createErrorResponse(409, "User with this email already exists");
    }

    logger.info("Creating platform admin user", {
      requestId,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
    });

    // Create new user record in database
    const user = await userService.createUser({
      email: data.email,
      password: data.password,
      firstName: data.firstName,
      lastName: data.lastName,
      role: UserRole.ADMIN,
      createdBy: "admin-registration",
    });

    // Update user type and remove merchant association
    await AppDataSource.getRepository(User).update(user.user_id, {
      user_type: UserType.PLATFORM_ADMIN,
      merchant_id: null,
    });

    logger.info("Platform admin created successfully", {
      requestId,
      clientIp,
      userId: user.user_id,
      email: user.email,
      fullName: user.full_name,
    });

    return createResponse(201, {
      success: true,
      message: "Platform admin created successfully",
      data: {
        userId: user.user_id,
        email: user.email,
        name: user.full_name,
        token: user.api_token!,
      },
    });
    */
  } catch (error) {
    logger.error("Error creating platform admin", {
      requestId,
      clientIp,
      error: (error as Error).message,
      stack: (error as Error).stack,
    });
    return createErrorResponse(500, "Internal server error", (error as Error).message);
  }
};
