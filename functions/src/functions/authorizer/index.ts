// ===================================================================
// AUTHORIZER DATABASE FUNCTIONALITY TEMPORARILY DISABLED
// ===================================================================
// Database operations have been commented out to disable database
// functionality. To re-enable: uncomment database-related code below
// ===================================================================

import { z } from "zod";
import type { APIGatewayTokenAuthorizerEvent, APIGatewayAuthorizerResult, Context } from "aws-lambda";
// import { AppDataSource } from "../../data-source/index.js";
// import { UserService } from "../../service/user.service.js";
import { logger } from "../../helpers/logger.js";

const AuthorizerEventSchema = z.object({
  type: z.literal("TOKEN"),
  authorizationToken: z.string(),
  methodArn: z.string(),
});

function parseMethodArn(methodArn: string): { method: string; resource: string } {
  const parts = methodArn.split("/");
  return {
    method: parts[parts.length - 2] || "",
    resource: parts[parts.length - 1] || "",
  };
}

function extractBearerToken(authHeader: string): string {
  if (!authHeader?.startsWith("Bearer ")) {
    throw new Error("Missing or invalid authorization header");
  }

  const token = authHeader.replace("Bearer ", "").trim();
  if (!token) {
    throw new Error("Empty bearer token");
  }

  return token;
}

async function validateUserToken(_token: string) {
  // Database functionality is temporarily disabled
  throw new Error("Database functionality is temporarily disabled");

  /*
  const userService = new UserService();
  const user = await userService.validateToken(token);

  if (!user) {
    throw new Error("Invalid or expired token");
  }

  return user;
  */
}

function isAuthorizedUser(user: any): boolean {
  if (user.is_platform_admin) {
    return true;
  }

  if (user.is_admin) {
    return true;
  }

  return false;
}

function generatePolicy(
  principalId: string,
  effect: "Allow" | "Deny",
  resource: string,
  context: Record<string, string> = {}
): APIGatewayAuthorizerResult {
  const policy: APIGatewayAuthorizerResult = {
    principalId,
    policyDocument: {
      Version: "2012-10-17",
      Statement: [
        {
          Action: "execute-api:Invoke",
          Effect: effect,
          Resource: resource,
        },
      ],
    },
    context,
  };

  logger.debug("Generated policy", { principalId, effect, hasContext: Object.keys(context).length > 0 });
  return policy;
}

export const handler = async (event: APIGatewayTokenAuthorizerEvent, _context: Context): Promise<APIGatewayAuthorizerResult> => {
  logger.info("Authorizer invoked", { methodArn: event.methodArn });

  try {
    const validatedEvent = AuthorizerEventSchema.parse(event);

    // Database functionality is temporarily disabled
    logger.warn("⚠️  Database functionality is temporarily disabled - denying all requests");
    throw new Error("Database functionality is temporarily disabled");

    /*
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    const { method, resource } = parseMethodArn(validatedEvent.methodArn);
    logger.info("Processing request", { method, resource });

    const token = extractBearerToken(validatedEvent.authorizationToken);

    // Validate token and fetch user from database
    const user = await validateUserToken(token);

    if (!user.is_active) {
      throw new Error("User account is inactive");
    }

    if (!isAuthorizedUser(user)) {
      throw new Error(`Insufficient privileges for ${user.email}`);
    }

    logger.info("Authorization successful", {
      email: user.email,
      role: user.role,
      userType: user.user_type,
    });

    const context: Record<string, string> = {
      userId: user.user_id,
      email: user.email,
      role: user.role,
      userType: user.user_type,
    };

    return generatePolicy(user.user_id, "Allow", validatedEvent.methodArn, context);
    */
  } catch (error) {
    const message = error instanceof Error ? error.message : "Authorization failed";
    logger.error("Authorization denied", { error: message });
    throw new Error("Unauthorized");
  }
};
