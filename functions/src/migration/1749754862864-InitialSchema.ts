// ===================================================================
// MIGRATION FILE TEMPORARILY DISABLED
// ===================================================================
// All TypeORM migration functionality has been commented out to disable
// database functionality. To re-enable: uncomment all code below
// ===================================================================

/*
import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialSchema1749754862864 implements MigrationInterface {
    name = 'InitialSchema1749754862864'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "merchant_bank_accounts" ("account_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "merchant_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "account_type" character varying(10) NOT NULL DEFAULT 'all', "is_primary" boolean NOT NULL DEFAULT false, "account_method" smallint NOT NULL, "account_number" character varying(255) NOT NULL, "routing_number" character varying(50) NOT NULL, "account_name" character varying(255), "currency" character varying(3) NOT NULL, "status" smallint NOT NULL DEFAULT '0', "verification_date" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_65ec7738727a849bec8c58b43e8" PRIMARY KEY ("account_id"))`);
        await queryRunner.query(`CREATE TABLE "merchant_documents" ("document_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "merchant_id" uuid NOT NULL, "member_id" uuid, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "document_type" smallint NOT NULL, "document_name" character varying(255) NOT NULL, "document_path" character varying(1024) NOT NULL, "mime_type" character varying(100) NOT NULL, "file_size" integer NOT NULL, "verification_status" smallint NOT NULL DEFAULT '0', "verification_date" TIMESTAMP WITH TIME ZONE, "verified_by" character varying(50), CONSTRAINT "PK_2c565f0381578c016d543a6eb0c" PRIMARY KEY ("document_id"))`);
        await queryRunner.query(`CREATE TABLE "merchant_members" ("member_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "merchant_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "title" character varying(100), "first_name" character varying(100) NOT NULL, "last_name" character varying(100) NOT NULL, "ssn" character varying(20) NOT NULL, "date_of_birth" date NOT NULL, "ownership_percentage" smallint NOT NULL, "significant_responsibility" boolean NOT NULL DEFAULT false, "politically_exposed" boolean NOT NULL DEFAULT false, "email" character varying(255) NOT NULL, "phone" character varying(20) NOT NULL, "is_primary" boolean NOT NULL DEFAULT false, "address1" character varying(255) NOT NULL, "address2" character varying(255), "city" character varying(100) NOT NULL, "state" character varying(50) NOT NULL, "zip" character varying(20) NOT NULL, "country" character varying(50) NOT NULL, "id_verification_status" smallint NOT NULL DEFAULT '0', "background_check_status" smallint NOT NULL DEFAULT '0', CONSTRAINT "PK_1615df4b158b0abd82ede83b719" PRIMARY KEY ("member_id"))`);
        await queryRunner.query(`CREATE TABLE "merchant_onboarding_status" ("status_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "merchant_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "current_step" smallint NOT NULL DEFAULT '1', "is_completed" boolean NOT NULL DEFAULT false, "completion_date" TIMESTAMP WITH TIME ZONE, "entity_info_completed" boolean NOT NULL DEFAULT false, "bank_info_completed" boolean NOT NULL DEFAULT false, "owner_info_completed" boolean NOT NULL DEFAULT false, "documents_uploaded" boolean NOT NULL DEFAULT false, "verification_completed" boolean NOT NULL DEFAULT false, "agreement_accepted" boolean NOT NULL DEFAULT false, "kyc_status" smallint NOT NULL DEFAULT '0', "aml_status" smallint NOT NULL DEFAULT '0', "underwriting_status" smallint NOT NULL DEFAULT '0', "is_automated_onboarding" boolean NOT NULL DEFAULT false, "notes" text, CONSTRAINT "REL_9acad834246f0d034ed266b4f2" UNIQUE ("merchant_id"), CONSTRAINT "PK_bc7511010014aa2affbc6ec7e10" PRIMARY KEY ("status_id"))`);
        await queryRunner.query(`CREATE TABLE "merchant_notes" ("note_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "merchant_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "created_by" character varying(255) NOT NULL, "note_text" text NOT NULL, "note_type" smallint NOT NULL DEFAULT '0', "is_internal" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_463dd3c83e886e4c5fe00ec3c63" PRIMARY KEY ("note_id"))`);
        await queryRunner.query(`CREATE TABLE "merchant_audit_log" ("log_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "merchant_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "table_name" character varying(50) NOT NULL, "record_id" uuid NOT NULL, "action" character varying(10) NOT NULL, "changed_by" character varying(255) NOT NULL, "ip_address" character varying(50), "changes" jsonb NOT NULL, CONSTRAINT "PK_7112b61d1b7f1a02fd73a3383dc" PRIMARY KEY ("log_id"))`);
        await queryRunner.query(`CREATE TYPE "public"."users_user_type_enum" AS ENUM('platform_admin', 'merchant_user')`);
        await queryRunner.query(`CREATE TYPE "public"."users_role_enum" AS ENUM('admin', 'user', 'super_admin', 'merchant_admin', 'merchant_user', 'merchant_viewer')`);
        await queryRunner.query(`CREATE TABLE "users" ("user_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "email" character varying(255) NOT NULL, "password_hash" character varying(255) NOT NULL, "first_name" character varying(100), "last_name" character varying(100), "merchant_id" uuid, "user_type" "public"."users_user_type_enum" NOT NULL DEFAULT 'merchant_user', "title" character varying(100), "department" character varying(100), "permissions" json, "role" "public"."users_role_enum" NOT NULL DEFAULT 'user', "status" smallint NOT NULL DEFAULT '1', "api_token" character varying(255), "api_token_expires_at" TIMESTAMP WITH TIME ZONE, "last_login_at" TIMESTAMP WITH TIME ZONE, "last_login_ip" inet, "failed_login_attempts" smallint NOT NULL DEFAULT '0', "locked_until" TIMESTAMP WITH TIME ZONE, "two_factor_enabled" boolean NOT NULL DEFAULT false, "two_factor_secret" character varying(255), "last_password_change" TIMESTAMP WITH TIME ZONE, "ip_whitelist" json, "compliance_training_completed" TIMESTAMP WITH TIME ZONE, "email_verified" boolean NOT NULL DEFAULT false, "email_verification_token" character varying(255), "email_verified_at" TIMESTAMP WITH TIME ZONE, "password_reset_token" character varying(255), "password_reset_expires_at" TIMESTAMP WITH TIME ZONE, "timezone" character varying(50) NOT NULL DEFAULT 'UTC', "language_preference" character varying(10) NOT NULL DEFAULT 'en', "notification_preferences" json, "session_timeout_minutes" smallint NOT NULL DEFAULT '30', "created_by" character varying(50), "updated_by" character varying(50), CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "UQ_b0c7bff7a2c2f7f12d3a90b4f33" UNIQUE ("api_token"), CONSTRAINT "PK_96aac72f1574b88752e9fb00089" PRIMARY KEY ("user_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_fe996f039efa99e46d75761aad" ON "users" ("merchant_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_c063149bd1bbc34874671d5c1d" ON "users" ("api_token") WHERE api_token IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_97672ac88f789774dd47f7c8be" ON "users" ("email") `);
        await queryRunner.query(`CREATE TABLE "merchants" ("merchant_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "entity_type" smallint NOT NULL DEFAULT '1', "legal_name" character varying(255) NOT NULL, "business_name" character varying(255), "address1" character varying(255), "address2" character varying(255), "city" character varying(100), "state" character varying(50), "zip" character varying(20), "country" character varying(50) NOT NULL DEFAULT 'US', "phone" character varying(20) NOT NULL, "email" character varying(255) NOT NULL, "ein" character varying(20), "website" character varying(255), "external_entity_id" character varying(100), "tc_version" character varying(10) NOT NULL DEFAULT '1.0', "tc_acceptance_date" TIMESTAMP WITH TIME ZONE, "tc_ip_address" inet, "currency" character varying(3) NOT NULL DEFAULT 'USD', "dba_name" character varying(255), "is_public" boolean NOT NULL DEFAULT false, "is_new" boolean NOT NULL DEFAULT true, "mcc" character varying(4) NOT NULL DEFAULT '5999', "status" smallint NOT NULL DEFAULT '1', "annual_cc_sales" numeric(15,2), "avg_ticket" numeric(10,2), "established_date" date, "verification_status" smallint NOT NULL DEFAULT '0', "risk_score" smallint, "high_risk_business" boolean NOT NULL DEFAULT false, "regulatory_notes" text, "suspicious_activity_flags" json, "approval_date" TIMESTAMP WITH TIME ZONE, "approved_by" character varying(50), "processing_limits" json, "settlement_schedule" json, "customer_service_contact" json, CONSTRAINT "UQ_7682193bcf281285d0a459c4b1e" UNIQUE ("email"), CONSTRAINT "PK_829e43b4ae5e536cd560a83f0dc" PRIMARY KEY ("merchant_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_84f1d5ba14e3d8ec381d885248" ON "merchants" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_ac5b4069e5cd01b28cd859fb51" ON "merchants" ("verification_status") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_14fffb526577e2ed7846b0f378" ON "merchants" ("ein") WHERE ein IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_7682193bcf281285d0a459c4b1" ON "merchants" ("email") `);
        await queryRunner.query(`ALTER TABLE "merchant_bank_accounts" ADD CONSTRAINT "FK_6547a5bab57714416db527ba19f" FOREIGN KEY ("merchant_id") REFERENCES "merchants"("merchant_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "merchant_documents" ADD CONSTRAINT "FK_d09dd0ffd48b414fefd02409416" FOREIGN KEY ("merchant_id") REFERENCES "merchants"("merchant_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "merchant_documents" ADD CONSTRAINT "FK_3f306d6e867fb947eaf2a9ae6ed" FOREIGN KEY ("member_id") REFERENCES "merchant_members"("member_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "merchant_members" ADD CONSTRAINT "FK_5f007086ad43497dd53f5892872" FOREIGN KEY ("merchant_id") REFERENCES "merchants"("merchant_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "merchant_onboarding_status" ADD CONSTRAINT "FK_9acad834246f0d034ed266b4f2c" FOREIGN KEY ("merchant_id") REFERENCES "merchants"("merchant_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "merchant_notes" ADD CONSTRAINT "FK_ffbeadff45290bac585410b963c" FOREIGN KEY ("merchant_id") REFERENCES "merchants"("merchant_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "merchant_audit_log" ADD CONSTRAINT "FK_05a881fad7ebdc90fe4b8abe402" FOREIGN KEY ("merchant_id") REFERENCES "merchants"("merchant_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_fe996f039efa99e46d75761aad0" FOREIGN KEY ("merchant_id") REFERENCES "merchants"("merchant_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_fe996f039efa99e46d75761aad0"`);
        await queryRunner.query(`ALTER TABLE "merchant_audit_log" DROP CONSTRAINT "FK_05a881fad7ebdc90fe4b8abe402"`);
        await queryRunner.query(`ALTER TABLE "merchant_notes" DROP CONSTRAINT "FK_ffbeadff45290bac585410b963c"`);
        await queryRunner.query(`ALTER TABLE "merchant_onboarding_status" DROP CONSTRAINT "FK_9acad834246f0d034ed266b4f2c"`);
        await queryRunner.query(`ALTER TABLE "merchant_members" DROP CONSTRAINT "FK_5f007086ad43497dd53f5892872"`);
        await queryRunner.query(`ALTER TABLE "merchant_documents" DROP CONSTRAINT "FK_3f306d6e867fb947eaf2a9ae6ed"`);
        await queryRunner.query(`ALTER TABLE "merchant_documents" DROP CONSTRAINT "FK_d09dd0ffd48b414fefd02409416"`);
        await queryRunner.query(`ALTER TABLE "merchant_bank_accounts" DROP CONSTRAINT "FK_6547a5bab57714416db527ba19f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7682193bcf281285d0a459c4b1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_14fffb526577e2ed7846b0f378"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ac5b4069e5cd01b28cd859fb51"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_84f1d5ba14e3d8ec381d885248"`);
        await queryRunner.query(`DROP TABLE "merchants"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_97672ac88f789774dd47f7c8be"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c063149bd1bbc34874671d5c1d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fe996f039efa99e46d75761aad"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_user_type_enum"`);
        await queryRunner.query(`DROP TABLE "merchant_audit_log"`);
        await queryRunner.query(`DROP TABLE "merchant_notes"`);
        await queryRunner.query(`DROP TABLE "merchant_onboarding_status"`);
        await queryRunner.query(`DROP TABLE "merchant_members"`);
        await queryRunner.query(`DROP TABLE "merchant_documents"`);
        await queryRunner.query(`DROP TABLE "merchant_bank_accounts"`);
    }

}
*/

// Mock migration class for when database is disabled
export class InitialSchema1749754862864 {
  name = "InitialSchema1749754862864";

  public async up(_queryRunner: unknown): Promise<void> {
    throw new Error("Database functionality is temporarily disabled");
  }

  public async down(_queryRunner: unknown): Promise<void> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
