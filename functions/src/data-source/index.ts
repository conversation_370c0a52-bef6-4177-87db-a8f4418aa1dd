// ===================================================================
// DATABASE FUNCTIONALITY TEMPORARILY DISABLED
// ===================================================================
// This file contains the main TypeORM DataSource configuration.
// All database functionality has been commented out to allow the
// application to run without database dependencies.
//
// To re-enable database functionality:
// 1. Uncomment all the code below
// 2. Uncomment entity imports and exports
// 3. Uncomment repository and service files
// 4. Uncomment database operations in Lambda functions
// 5. Uncomment database environment variables
// ===================================================================

/*
import "reflect-metadata";
import { DataSource } from "typeorm";
import { config } from "dotenv";
import path from "path";
import { fileURLToPath } from "url";
import {
  Merchant,
  MerchantMember,
  MerchantBankAccount,
  MerchantDocument,
  MerchantOnboardingStatus,
  MerchantNote,
  MerchantAuditLog,
  User,
} from "../entity/index.js";
*/

/*
// Lambda-compatible directory resolution
const getCurrentDir = (): string => {
  // In Lambda environment, use the fixed path
  if (process.env.AWS_LAMBDA_FUNCTION_NAME) {
    return "/var/task/src/data-source";
  }
  // In local/development environment, use import.meta.url
  if (import.meta.url) {
    return path.dirname(fileURLToPath(import.meta.url));
  }
  // Fallback
  return "/var/task/src/data-source";
};

const __dirname: string = getCurrentDir();
const functionsDir = path.resolve(__dirname, "../../");
config({ path: path.join(functionsDir, ".env") });

const getDbConfig = () => {
  // For local development, force localhost when using SSH tunnel
  const isLocal = process.env.IS_OFFLINE;

  return {
    host: isLocal ? "127.0.0.1" : process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || "5432"),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
  };
};

const dbConfig = getDbConfig();

export const AppDataSource = new DataSource({
  type: "postgres",
  host: dbConfig.host,
  port: dbConfig.port,
  username: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  synchronize: false,
  logging: process.env.NODE_ENV === "development",
  entities: [Merchant, MerchantMember, MerchantBankAccount, MerchantDocument, MerchantOnboardingStatus, MerchantNote, MerchantAuditLog, User],
  migrations: [path.join(__dirname, "../migration/**/ //*.{ts,js}")],
//   migrationsTableName: "migrations",
//   migrationsRun: false,
//   migrationsTransactionMode: "each",
//   extra: {
//     connectionTimeoutMillis: 10000,
//     idleTimeoutMillis: 30000,
//     max: 5,
//   },
// });

// Mock AppDataSource for when database is disabled
export const AppDataSource = {
  isInitialized: false,
  initialize: async () => {
    console.log("⚠️  Database functionality is temporarily disabled");
    return Promise.resolve();
  },
  destroy: async () => {
    console.log("⚠️  Database functionality is temporarily disabled");
    return Promise.resolve();
  },
  getRepository: () => {
    throw new Error("Database functionality is temporarily disabled");
  },
};
