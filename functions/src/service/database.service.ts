// ===================================================================
// DATABASE SERVICE TEMPORARILY DISABLED
// ===================================================================
// This file contains the DatabaseService class for direct PostgreSQL
// connections. All database functionality has been commented out.
//
// To re-enable: Uncomment all the code below
// ===================================================================

/*
import { SecretsManagerClient, GetSecretValueCommand } from "@aws-sdk/client-secrets-manager";
import pg from "pg";

const { Client } = pg;

interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
}

interface DatabaseCredentials {
  username: string;
  password: string;
}

export class DatabaseService {
  private client: pg.Client | null = null;

  async initialize(): Promise<void> {
    if (this.client) return;

    try {
      const dbConfig: DatabaseConfig = {
        host: process.env.DB_HOST!,
        port: parseInt(process.env.DB_PORT || "5432"),
        database: process.env.DB_NAME!,
        user: process.env.DB_USERNAME!,
        password: process.env.DB_PASSWORD!,
      };

      // Only get database credentials from Secrets Manager when running in actual AWS Lambda
      // NOT when running locally with serverless offline
      if (process.env.AWS_LAMBDA_FUNCTION_NAME && !process.env.IS_OFFLINE) {
        const secretArn = process.env.DB_SECRET_ARN;
        if (secretArn) {
          const secretsManager = new SecretsManagerClient({ region: process.env.AWS_REGION });
          const command = new GetSecretValueCommand({ SecretId: secretArn });
          const response = await secretsManager.send(command);

          if (response.SecretString) {
            const credentials: DatabaseCredentials = JSON.parse(response.SecretString);
            dbConfig.user = credentials.username;
            dbConfig.password = credentials.password;
          }
        }
      }

      this.client = new Client(dbConfig);
      await this.client.connect();
    } catch (error) {
      console.error("Database initialization failed:", error);
      throw error;
    }
  }

  async query(text: string, params?: any[]): Promise<pg.QueryResult> {
    if (!this.client) {
      throw new Error("Database not initialized. Call initialize() first.");
    }
    return this.client.query(text, params);
  }

  async close(): Promise<void> {
    if (this.client) {
      await this.client.end();
      this.client = null;
    }
  }
}
*/

// Mock DatabaseService for when database is disabled
export class DatabaseService {
  private client: any = null;

  async initialize(): Promise<void> {
    console.log("⚠️  Database functionality is temporarily disabled");
    return Promise.resolve();
  }

  async query(_text: string, _params?: any[]): Promise<any> {
    throw new Error("Database functionality is temporarily disabled");
  }

  async close(): Promise<void> {
    console.log("⚠️  Database functionality is temporarily disabled");
    return Promise.resolve();
  }
}
