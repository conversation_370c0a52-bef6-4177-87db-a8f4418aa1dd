import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, PutCommand, GetCommand, DeleteCommand } from "@aws-sdk/lib-dynamodb";
import { logger } from "../helpers/logger.js";

export interface TokenData {
  merchantId: string;
  description: string;
  amount: number;
  returnUrl?: string;
  expiresAt: Date;
  used: boolean;
  // E-commerce compliance fields
  currency?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
    commodityCode?: string;
    productCode?: string;
  }>;
  taxAmount?: number;
  shippingAmount?: number;
  dutyAmount?: number;
  orderNumber?: string;
  invoiceNumber?: string;
  customerCode?: string;
  orderDiscount?: number;
  // Merchant information
  merchantInfo?: {
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
    contactEmail?: string;
    contactPhone?: string;
  };
}

export interface StoredTokenData {
  merchantId: string;
  description: string;
  amount: number;
  returnUrl?: string;
  expiresAt: string; // ISO string for JSON serialization
  used: boolean;
  // E-commerce compliance fields
  currency?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
    commodityCode?: string;
    productCode?: string;
  }>;
  taxAmount?: number;
  shippingAmount?: number;
  dutyAmount?: number;
  orderNumber?: string;
  invoiceNumber?: string;
  customerCode?: string;
  orderDiscount?: number;
  // Merchant information
  merchantInfo?: {
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
    contactEmail?: string;
    contactPhone?: string;
  };
}

interface DynamoTokenItem extends Omit<TokenData, "expiresAt"> {
  tokenId: string;
  expiresAt: number; // Unix timestamp in seconds for DynamoDB TTL
}

// Validation schemas
const REQUIRED_FIELDS = ["merchantId", "description", "amount", "expiresAt"] as const;
const MAX_DESCRIPTION_LENGTH = 500;
const MAX_AMOUNT = *********; // $9,999,999.99 in cents
const MIN_AMOUNT = 1; // $0.01 in cents

// DynamoDB client singleton
let dynamoClient: DynamoDBDocumentClient | null = null;
const tableName = process.env.PAYMENT_TOKENS_TABLE_NAME || "PaymentTokens-dev";

const clientConfig: { region: string; profile?: string } = {
  region: process.env.AWS_REGION || "us-east-1",
  profile: process.env.STAGE && process.env.STAGE === "dev" ? "payrix" : "default",
};

function getDynamoClient(): DynamoDBDocumentClient {
  if (!dynamoClient) {
    const client = new DynamoDBClient({
      region: clientConfig?.region,
      profile: clientConfig?.profile,
    });
    dynamoClient = DynamoDBDocumentClient.from(client, {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    });
  }
  return dynamoClient;
}

// Helper functions
function toDynamoItem(token: string, data: TokenData): DynamoTokenItem {
  return {
    tokenId: token,
    ...data,
    expiresAt: Math.floor(data.expiresAt.getTime() / 1000), // Convert to Unix timestamp in seconds for TTL
  };
}

function fromDynamoItem(item: DynamoTokenItem): TokenData {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { tokenId, expiresAt, ...rest } = item;
  return {
    ...rest,
    expiresAt: new Date(expiresAt * 1000), // Convert from Unix timestamp to Date
  };
}

// Validation functions
export function validateTokenData(data: Partial<TokenData>): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check required fields
  for (const field of REQUIRED_FIELDS) {
    if (!data[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Validate amount
  if (data.amount !== undefined) {
    if (typeof data.amount !== "number" || data.amount < MIN_AMOUNT || data.amount > MAX_AMOUNT) {
      errors.push(`Amount must be between ${MIN_AMOUNT} and ${MAX_AMOUNT} cents`);
    }
  }

  // Validate description length
  if (data.description && data.description.length > MAX_DESCRIPTION_LENGTH) {
    errors.push(`Description must be ${MAX_DESCRIPTION_LENGTH} characters or less`);
  }

  // Validate expiration date
  if (data.expiresAt && data.expiresAt <= new Date()) {
    errors.push("Expiration date must be in the future");
  }

  // Validate items if provided
  if (data.items) {
    if (!Array.isArray(data.items) || data.items.length === 0) {
      errors.push("Items must be a non-empty array");
    } else {
      data.items.forEach((item, index) => {
        if (!item.name || typeof item.name !== "string") {
          errors.push(`Item ${index + 1}: name is required`);
        }
        if (typeof item.quantity !== "number" || item.quantity <= 0) {
          errors.push(`Item ${index + 1}: quantity must be a positive number`);
        }
        if (typeof item.unitPrice !== "number" || item.unitPrice < 0) {
          errors.push(`Item ${index + 1}: unitPrice must be a non-negative number`);
        }
        if (typeof item.total !== "number" || item.total < 0) {
          errors.push(`Item ${index + 1}: total must be a non-negative number`);
        }
      });
    }
  }

  return { isValid: errors.length === 0, errors };
}

// Core token storage functions
export async function setToken(token: string, data: TokenData): Promise<void> {
  try {
    const item = toDynamoItem(token, data);
    const client = getDynamoClient();

    await client.send(
      new PutCommand({
        TableName: tableName,
        Item: item,
      })
    );
  } catch (error) {
    logger.error("Error storing token", { error });
    throw new Error(`Failed to store token: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

export async function getToken(token: string): Promise<TokenData | null> {
  try {
    const client = getDynamoClient();
    const result = await client.send(
      new GetCommand({
        TableName: tableName,
        Key: { tokenId: token },
      })
    );

    if (!result.Item) {
      return null;
    }

    return fromDynamoItem(result.Item as DynamoTokenItem);
  } catch (error) {
    logger.error("Error retrieving token", { error });
    throw new Error(`Failed to retrieve token: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

export async function deleteToken(token: string): Promise<void> {
  try {
    const client = getDynamoClient();
    await client.send(
      new DeleteCommand({
        TableName: tableName,
        Key: { tokenId: token },
      })
    );
  } catch (error) {
    logger.error("Error deleting token", { error });
    throw new Error(`Failed to delete token: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

export async function markTokenAsUsed(token: string): Promise<boolean> {
  try {
    const tokenData = await getToken(token);
    if (!tokenData) {
      logger.error("Token not found", { token });
      return false;
    }

    if (tokenData.used) {
      logger.error("Token already used", { token });
      return false;
    }

    if (tokenData.expiresAt <= new Date()) {
      logger.error("Token expired", { token });
      return false;
    }

    // Update the token to mark it as used
    tokenData.used = true;
    await setToken(token, tokenData);
    return true;
  } catch (error) {
    logger.error("Error marking token as used", { error });
    return false;
  }
}

export function toStoredTokenData(data: TokenData): StoredTokenData {
  return {
    ...data,
    expiresAt: data.expiresAt.toISOString(),
  };
}

export function fromStoredTokenData(data: StoredTokenData): TokenData {
  return {
    ...data,
    expiresAt: new Date(data.expiresAt),
  };
}

export async function validateToken(token: string): Promise<{
  isValid: boolean;
  data?: TokenData;
  error?: string;
}> {
  try {
    const tokenData = await getToken(token);

    if (!tokenData) {
      return {
        isValid: false,
        error: "Token not found",
      };
    }

    if (tokenData.used) {
      return {
        isValid: false,
        error: "Token already used",
      };
    }

    if (tokenData.expiresAt <= new Date()) {
      return {
        isValid: false,
        error: "Token expired",
      };
    }

    return {
      isValid: true,
      data: tokenData,
    };
  } catch (error) {
    logger.error("Error validating token", { error });
    return {
      isValid: false,
      error: "Token validation failed",
    };
  }
}
