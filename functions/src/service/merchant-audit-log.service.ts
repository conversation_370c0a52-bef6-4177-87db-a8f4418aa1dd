// ===================================================================
// MERCHANT AUDIT LOG SERVICE TEMPORARILY DISABLED
// ===================================================================
/*
import { MerchantAuditLog } from "../entity/merchant-audit-log.entity.ts";
import { MerchantAuditLogRepository } from "../repository/merchant-audit-log.repository.ts";
import { getCustomRepository } from "typeorm";

export class MerchantAuditLogService {
  private auditLogRepository: MerchantAuditLogRepository;

  constructor() {
    this.auditLogRepository = getCustomRepository(MerchantAuditLogRepository);
  }

  async findById(logId: string): Promise<MerchantAuditLog | undefined> {
    const log = await this.auditLogRepository.findOne({ where: { log_id: logId } });
    return log ?? undefined;
  }

  async findByMerchant(merchantId: string, limit: number = 50): Promise<MerchantAuditLog[]> {
    return this.auditLogRepository.findByMerchant(merchantId, limit);
  }

  async findByTable(merchantId: string, tableName: string): Promise<MerchantAuditLog[]> {
    return this.auditLogRepository.findByTable(merchantId, tableName);
  }

  async findByRecordId(recordId: string): Promise<MerchantAuditLog[]> {
    return this.auditLogRepository.findByRecordId(recordId);
  }

  async findByDateRange(merchantId: string, startDate: Date, endDate: Date): Promise<MerchantAuditLog[]> {
    return this.auditLogRepository.findByDateRange(merchantId, startDate, endDate);
  }

  async createLogEntry(
    merchantId: string,
    tableName: string,
    recordId: string,
    action: string,
    changedBy: string,
    changes: any,
    ipAddress?: string
  ): Promise<MerchantAuditLog> {
    const logEntry = await this.auditLogRepository.createLogEntry(merchantId, tableName, recordId, action, changedBy, changes, ipAddress);

    if (!logEntry) {
      throw new Error("Failed to create audit log entry");
    }

    return logEntry;
  }

  async getUserActivitySummary(userId: string, startDate: Date, endDate: Date): Promise<any> {
    const logs = await this.auditLogRepository
      .createQueryBuilder("log")
      .where("log.changed_by = :userId", { userId })
      .andWhere("log.created_at >= :startDate", { startDate })
      .andWhere("log.created_at <= :endDate", { endDate })
      .orderBy("log.created_at", "DESC")
      .getMany();

    const summary = {
      totalActions: logs.length,
      actionsByTable: this.groupByTableAndAction(logs),
      merchantsModified: new Set(logs.map((log) => log.merchant_id)).size,
      timeline: this.groupByDay(logs),
    };

    return summary;
  }

  private groupByTableAndAction(logs: MerchantAuditLog[]): Record<string, Record<string, number>> {
    const result: Record<string, Record<string, number>> = {};

    logs.forEach((log) => {
      if (!result[log.table_name]) {
        result[log.table_name] = {};
      }

      if (!result[log.table_name][log.action]) {
        result[log.table_name][log.action] = 0;
      }

      result[log.table_name][log.action]++;
    });

    return result;
  }

  private groupByDay(logs: MerchantAuditLog[]): Record<string, number> {
    const result: Record<string, number> = {};

    logs.forEach((log) => {
      const day = log.created_at.toISOString().split("T")[0];

      if (!result[day]) {
        result[day] = 0;
      }

      result[day]++;
    });

    return result;
  }
}
*/

// Mock MerchantAuditLogService for when database is disabled
export class MerchantAuditLogService {
  async findById(_logId: string): Promise<undefined> {
    throw new Error("Database functionality is temporarily disabled");
  }

  async findByMerchant(_merchantId: string, _limit = 50): Promise<never[]> {
    throw new Error("Database functionality is temporarily disabled");
  }

  async createLogEntry(
    _merchantId: string,
    _tableName: string,
    _recordId: string,
    _action: string,
    _changedBy: string,
    _changes: any,
    _ipAddress?: string
  ): Promise<never> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
