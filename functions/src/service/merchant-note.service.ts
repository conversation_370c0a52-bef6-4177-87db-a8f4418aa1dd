// ===================================================================
// MERCHANT NOTE SERVICE TEMPORARILY DISABLED
// ===================================================================
/*
// src/service/merchant-note.service.ts
import { MerchantNote } from "../entity/merchant-note.entity.ts";
import { MerchantNoteRepository } from "../repository/merchant-note.repository.ts";
import { MerchantAuditLogService } from "./merchant-audit-log.service.ts";
import { getCustomRepository } from "typeorm";

export class MerchantNoteService {
  private noteRepository: MerchantNoteRepository;
  private auditLogService: MerchantAuditLogService;

  constructor() {
    this.noteRepository = getCustomRepository(MerchantNoteRepository);
    this.auditLogService = new MerchantAuditLogService();
  }

  async findById(noteId: string): Promise<MerchantNote | undefined> {
    const note = await this.noteRepository.findOne({ where: { note_id: noteId } });
    return note ?? undefined;
  }

  async findByMerchant(merchantId: string, includeInternal: boolean = true): Promise<MerchantNote[]> {
    return this.noteRepository.findByMerchant(merchantId, includeInternal);
  }

  async findByType(merchantId: string, noteType: number): Promise<MerchantNote[]> {
    return this.noteRepository.findByType(merchantId, noteType);
  }

  async createNote(
    merchantId: string,
    createdBy: string,
    noteText: string,
    noteType: number = 0,
    isInternal: boolean = true,
    ipAddress?: string
  ): Promise<MerchantNote> {
    // Create the note
    const note = await this.noteRepository.createNote(merchantId, createdBy, noteText, noteType, isInternal);

    // Log the creation
    await this.auditLogService.createLogEntry(merchantId, "merchant_notes", note?.note_id ?? "", "INSERT", createdBy, { new: note }, ipAddress);

    if (!note) {
      throw new Error("Failed to create note");
    }

    return note;
  }

  async updateNoteText(noteId: string, noteText: string, userId: string, ipAddress?: string): Promise<MerchantNote> {
    // Get current data for audit logging
    const oldNote = await this.noteRepository.findOne({ where: { note_id: noteId } });
    if (!oldNote) {
      throw new Error(`Note not found: ${noteId}`);
    }

    // Update note
    await this.noteRepository.update(noteId, { note_text: noteText });

    // Get updated data
    const updatedNote = await this.noteRepository.findOne({ where: { note_id: noteId } });

    // Log the update
    await this.auditLogService.createLogEntry(
      oldNote.merchant_id,
      "merchant_notes",
      noteId,
      "UPDATE",
      userId,
      {
        old: { note_text: oldNote.note_text },
        new: { note_text: updatedNote?.note_text },
      },
      ipAddress
    );

    if (!updatedNote) {
      throw new Error("Failed to update note");
    }

    return updatedNote;
  }

  async delete(noteId: string, userId: string, ipAddress?: string): Promise<void> {
    // Get current data for audit logging
    const note = await this.noteRepository.findOne({ where: { note_id: noteId } });
    if (!note) {
      throw new Error(`Note not found: ${noteId}`);
    }

    const merchantId = note.merchant_id;

    // Delete note
    await this.noteRepository.delete(noteId);

    // Log the deletion
    await this.auditLogService.createLogEntry(merchantId, "merchant_notes", noteId, "DELETE", userId, { old: note }, ipAddress);
  }
}
*/

// Mock MerchantNoteService for when database is disabled
export class MerchantNoteService {
  async findById(_noteId: string): Promise<undefined> {
    throw new Error("Database functionality is temporarily disabled");
  }

  async findByMerchant(_merchantId: string, _includeInternal = true): Promise<never[]> {
    throw new Error("Database functionality is temporarily disabled");
  }

  async createNote(
    _merchantId: string,
    _createdBy: string,
    _noteText: string,
    _noteType = 0,
    _isInternal = true,
    _ipAddress?: string
  ): Promise<never> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
