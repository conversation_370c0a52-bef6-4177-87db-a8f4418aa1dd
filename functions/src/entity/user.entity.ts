// ===================================================================
// USER ENTITY TEMPORARILY DISABLED
// ===================================================================
// All TypeORM entity definitions have been commented out to disable
// database functionality. To re-enable: uncomment all code below
// ===================================================================

/*
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from "typeorm";
import { Merchant } from "./merchant.entity.ts";
*/

export enum UserRole {
  ADMIN = "admin",
  USER = "user",
  SUPER_ADMIN = "super_admin",
  MERCHANT_ADMIN = "merchant_admin",
  MERCHANT_USER = "merchant_user",
  MERCHANT_VIEWER = "merchant_viewer",
}

export enum UserStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  SUSPENDED = 2,
  PENDING_VERIFICATION = 3,
}

export enum UserType {
  PLATFORM_ADMIN = "platform_admin",
  MERCHANT_USER = "merchant_user",
}

export interface NotificationPreferences {
  email_alerts: boolean;
  sms_alerts: boolean;
  push_notifications: boolean;
  transaction_alerts: boolean;
  security_alerts: boolean;
  marketing_emails: boolean;
  frequency: "immediate" | "daily" | "weekly";
}

/*
@Entity("users")
@Index(["email"], { unique: true })
@Index(["api_token"], { unique: true, where: "api_token IS NOT NULL" })
@Index(["merchant_id"])
export class User {
  @PrimaryGeneratedColumn("uuid")
  user_id: string;

  @CreateDateColumn({ type: "timestamp with time zone" })
  created_at: Date;

  @UpdateDateColumn({ type: "timestamp with time zone" })
  updated_at: Date;

  // Basic User Information
  @Column({ type: "varchar", length: 255, unique: true })
  email: string;

  @Column({ type: "varchar", length: 255 })
  password_hash: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  first_name: string | null;

  @Column({ type: "varchar", length: 100, nullable: true })
  last_name: string | null;

  // Business Context Fields
  @Column({ type: "uuid", nullable: true })
  merchant_id: string | null;

  @ManyToOne(() => Merchant, (merchant) => merchant.users, { nullable: true, onDelete: "SET NULL" })
  @JoinColumn({ name: "merchant_id" })
  merchant: Merchant | null;

  @Column({
    type: "enum",
    enum: UserType,
    default: UserType.MERCHANT_USER,
  })
  user_type: UserType;

  @Column({ type: "varchar", length: 100, nullable: true })
  title: string | null;

  @Column({ type: "varchar", length: 100, nullable: true })
  department: string | null;

  @Column({ type: "json", nullable: true })
  permissions: string[] | null;

  // Authentication & Authorization
  @Column({
    type: "enum",
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({
    type: "smallint",
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  // API Token for Bearer authentication
  @Column({ type: "varchar", length: 255, nullable: true, unique: true })
  api_token: string | null;

  @Column({ type: "timestamp with time zone", nullable: true })
  api_token_expires_at: Date | null;

  // Enhanced Security Fields
  @Column({ type: "timestamp with time zone", nullable: true })
  last_login_at: Date | null;

  @Column({ type: "inet", nullable: true })
  last_login_ip: string | null;

  @Column({ type: "smallint", default: 0 })
  failed_login_attempts: number;

  @Column({ type: "timestamp with time zone", nullable: true })
  locked_until: Date | null;

  @Column({ type: "boolean", default: false })
  two_factor_enabled: boolean;

  @Column({ type: "varchar", length: 255, nullable: true })
  two_factor_secret: string | null;

  @Column({ type: "timestamp with time zone", nullable: true })
  last_password_change: Date | null;

  @Column({ type: "json", nullable: true })
  ip_whitelist: string[] | null;

  @Column({ type: "timestamp with time zone", nullable: true })
  compliance_training_completed: Date | null;

  // Email Verification
  @Column({ type: "boolean", default: false })
  email_verified: boolean;

  @Column({ type: "varchar", length: 255, nullable: true })
  email_verification_token: string | null;

  @Column({ type: "timestamp with time zone", nullable: true })
  email_verified_at: Date | null;

  // Password Reset
  @Column({ type: "varchar", length: 255, nullable: true })
  password_reset_token: string | null;

  @Column({ type: "timestamp with time zone", nullable: true })
  password_reset_expires_at: Date | null;

  // Operational Fields
  @Column({ type: "varchar", length: 50, default: "UTC" })
  timezone: string;

  @Column({ type: "varchar", length: 10, default: "en" })
  language_preference: string;

  @Column({ type: "json", nullable: true })
  notification_preferences: NotificationPreferences | null;

  @Column({ type: "smallint", default: 30 })
  session_timeout_minutes: number;

  // Audit Fields
  @Column({ type: "varchar", length: 50, nullable: true })
  created_by: string | null;

  @Column({ type: "varchar", length: 50, nullable: true })
  updated_by: string | null;

  // Helper methods (getters)
  get full_name(): string {
    return `${this.first_name || ""} ${this.last_name || ""}`.trim();
  }

  get is_active(): boolean {
    return this.status === UserStatus.ACTIVE;
  }

  get is_admin(): boolean {
    return this.role === UserRole.ADMIN || this.role === UserRole.SUPER_ADMIN;
  }

  get is_merchant_admin(): boolean {
    return this.role === UserRole.MERCHANT_ADMIN;
  }

  get is_merchant_user(): boolean {
    return [UserRole.MERCHANT_ADMIN, UserRole.MERCHANT_USER, UserRole.MERCHANT_VIEWER].includes(this.role);
  }

  get is_platform_admin(): boolean {
    return this.user_type === UserType.PLATFORM_ADMIN;
  }

  get is_api_token_valid(): boolean {
    return !!(this.api_token && (!this.api_token_expires_at || this.api_token_expires_at > new Date()));
  }

  get is_locked(): boolean {
    return !!(this.locked_until && this.locked_until > new Date());
  }

  get is_two_factor_enabled(): boolean {
    return this.two_factor_enabled && !!this.two_factor_secret;
  }

  get is_ip_whitelisted(): boolean {
    return !!(this.ip_whitelist && this.ip_whitelist.length > 0);
  }

  get needs_password_change(): boolean {
    if (!this.last_password_change) return true;
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
    return this.last_password_change < ninetyDaysAgo;
  }

  get is_compliance_current(): boolean {
    if (!this.compliance_training_completed) return false;
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    return this.compliance_training_completed > oneYearAgo;
  }

  // Utility methods
  hasPermission(permission: string): boolean {
    if (!this.permissions) return false;
    return this.permissions.includes(permission);
  }

  isIpAllowed(ip: string): boolean {
    if (!this.ip_whitelist || this.ip_whitelist.length === 0) return true;
    return this.ip_whitelist.includes(ip);
  }

  getDefaultNotificationPreferences(): NotificationPreferences {
    return {
      email_alerts: true,
      sms_alerts: false,
      push_notifications: true,
      transaction_alerts: true,
      security_alerts: true,
      marketing_emails: false,
      frequency: "immediate",
    };
  }
}
*/

// Mock User class for when database is disabled
export class User {
  user_id?: string;
  email?: string;
  password_hash?: string;
  first_name?: string | null;
  last_name?: string | null;
  role?: UserRole;
  status?: UserStatus;
  user_type?: UserType;
  api_token?: string | null;
  api_token_expires_at?: Date | null;
  merchant_id?: string | null;
  is_active?: boolean;

  get full_name(): string {
    return `${this.first_name || ""} ${this.last_name || ""}`.trim();
  }

  get is_api_token_valid(): boolean {
    return !!(this.api_token && (!this.api_token_expires_at || this.api_token_expires_at > new Date()));
  }
}
