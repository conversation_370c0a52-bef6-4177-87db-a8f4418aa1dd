// ===================================================================
// ENTITY EXPORTS TEMPORARILY DISABLED
// ===================================================================
// All TypeORM entity exports have been commented out to disable
// database functionality. To re-enable: uncomment all exports below
// ===================================================================

/*
export { Merchant } from "./merchant.entity.ts";
export { MerchantMember } from "./merchant-member.entity.ts";
export { MerchantBankAccount } from "./merchant-bank-account.entity.ts";
export { MerchantDocument } from "./merchant-document.entity.ts";
export { MerchantOnboardingStatus } from "./merchant-onboarding-status.entity.ts";
export { MerchantNote } from "./merchant-note.entity.ts";
export { MerchantAuditLog } from "./merchant-audit-log.entity.ts";
export { User, UserRole, UserStatus } from "./user.entity.ts";
*/

// Mock exports for when database is disabled
export enum UserRole {
  ADMIN = "admin",
  USER = "user",
}

export enum UserStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum UserType {
  PLATFORM_ADMIN = "platform_admin",
  MERCHANT_USER = "merchant_user",
}

// Mock entity classes
export class Merchant {}
export class MerchantMember {}
export class MerchantBankAccount {}
export class MerchantDocument {}
export class MerchantOnboardingStatus {}
export class MerchantNote {}
export class MerchantAuditLog {}
export class User {}
