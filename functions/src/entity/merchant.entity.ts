// ===================================================================
// MERCHANT ENTITY TEMPORARILY DISABLED
// ===================================================================
// All TypeORM entity definitions have been commented out to disable
// database functionality. To re-enable: uncomment all code below
// ===================================================================

/*
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, OneToOne, Index } from "typeorm";
import { MerchantBankAccount } from "./merchant-bank-account.entity.ts";
import { MerchantMember } from "./merchant-member.entity.ts";
import { MerchantDocument } from "./merchant-document.entity.ts";
import { MerchantOnboardingStatus } from "./merchant-onboarding-status.entity.ts";
import { MerchantNote } from "./merchant-note.entity.ts";
import { MerchantAuditLog } from "./merchant-audit-log.entity.ts";
import { User } from "./user.entity.ts";
*/

export interface ProcessingLimits {
  daily_limit: number;
  monthly_limit: number;
  per_transaction_limit: number;
  currency: string;
}

export interface SettlementSchedule {
  frequency: "daily" | "weekly" | "monthly";
  day_of_week?: number; // 0-6 for weekly
  day_of_month?: number; // 1-31 for monthly
  hold_days: number;
}

export interface CustomerServiceContact {
  name: string;
  email: string;
  phone: string;
  role: string;
}

/*
@Entity("merchants")
@Index(["email"], { unique: true })
@Index(["ein"], { unique: true, where: "ein IS NOT NULL" })
@Index(["verification_status"])
@Index(["status"])
export class Merchant {
  @PrimaryGeneratedColumn("uuid")
  merchant_id: string;

  @CreateDateColumn({ type: "timestamp with time zone" })
  created_at: Date;

  @UpdateDateColumn({ type: "timestamp with time zone" })
  updated_at: Date;

  // Entity Information
  @Column({ type: "smallint", default: 1 })
  entity_type: number;

  @Column({ type: "varchar", length: 255 })
  legal_name: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  business_name: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  address1: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  address2: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  city: string;

  @Column({ type: "varchar", length: 50, nullable: true })
  state: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  zip: string;

  @Column({ type: "varchar", length: 50, default: "US" })
  country: string;

  @Column({ type: "varchar", length: 20 })
  phone: string;

  @Column({ type: "varchar", length: 255, unique: true })
  email: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  ein: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  website: string;

  @Column({ type: "varchar", length: 100, nullable: true })
  external_entity_id: string; // Payrix entity ID

  // Terms & Conditions
  @Column({ type: "varchar", length: 10, default: "1.0" })
  tc_version: string;

  @Column({ type: "timestamp with time zone", nullable: true })
  tc_acceptance_date: Date | null;

  @Column({ type: "inet", nullable: true })
  tc_ip_address: string | null;

  @Column({ type: "varchar", length: 3, default: "USD" })
  currency: string;

  // Merchant Business Information
  @Column({ type: "varchar", length: 255, nullable: true })
  dba_name: string;

  @Column({ type: "boolean", default: false })
  is_public: boolean;

  @Column({ type: "boolean", default: true })
  is_new: boolean;

  @Column({ type: "varchar", length: 4, default: "5999" })
  mcc: string;

  @Column({ type: "smallint", default: 1 })
  status: number;

  @Column({ type: "decimal", precision: 15, scale: 2, nullable: true })
  annual_cc_sales: number;

  @Column({ type: "decimal", precision: 10, scale: 2, nullable: true })
  avg_ticket: number;

  @Column({ type: "date", nullable: true })
  established_date: Date;

  // Verification and Risk Fields
  @Column({ type: "smallint", default: 0 })
  verification_status: number;

  @Column({ type: "smallint", nullable: true })
  risk_score: number;

  @Column({ type: "boolean", default: false })
  high_risk_business: boolean;

  @Column({ type: "text", nullable: true })
  regulatory_notes: string | null;

  @Column({ type: "json", nullable: true })
  suspicious_activity_flags: string[] | null;

  @Column({ type: "timestamp with time zone", nullable: true })
  approval_date: Date;

  @Column({ type: "varchar", length: 50, nullable: true })
  approved_by: string;

  // Operational Readiness Fields
  @Column({ type: "json", nullable: true })
  processing_limits: ProcessingLimits | null;

  @Column({ type: "json", nullable: true })
  settlement_schedule: SettlementSchedule | null;

  @Column({ type: "json", nullable: true })
  customer_service_contact: CustomerServiceContact | null;

  // Relations
  @OneToMany(() => User, (user) => user.merchant)
  users: User[];

  @OneToMany(() => MerchantBankAccount, (bankAccount) => bankAccount.merchant)
  bank_accounts: MerchantBankAccount[];

  @OneToMany(() => MerchantMember, (member) => member.merchant)
  members: MerchantMember[];

  @OneToMany(() => MerchantDocument, (document) => document.merchant)
  documents: MerchantDocument[];

  @OneToOne(() => MerchantOnboardingStatus, (onboardingStatus) => onboardingStatus.merchant)
  onboarding_status: MerchantOnboardingStatus;

  @OneToMany(() => MerchantNote, (note) => note.merchant)
  notes: MerchantNote[];

  @OneToMany(() => MerchantAuditLog, (auditLog) => auditLog.merchant)
  audit_logs: MerchantAuditLog[];

  // Helper methods
  get is_approved(): boolean {
    return this.verification_status === 2 && !!this.approval_date;
  }

  get is_high_risk(): boolean {
    return this.high_risk_business || (this.risk_score !== null && this.risk_score >= 75);
  }

  get has_valid_tc_acceptance(): boolean {
    return !!this.tc_acceptance_date && !!this.tc_version;
  }

  get has_suspicious_activity(): boolean {
    return !!(this.suspicious_activity_flags && this.suspicious_activity_flags.length > 0);
  }

  get needs_regulatory_review(): boolean {
    return this.is_high_risk || this.has_suspicious_activity || !!this.regulatory_notes;
  }

  getDefaultProcessingLimits(): ProcessingLimits {
    return {
      daily_limit: 10000,
      monthly_limit: 250000,
      per_transaction_limit: 2500,
      currency: this.currency || "USD",
    };
  }

  getDefaultSettlementSchedule(): SettlementSchedule {
    return {
      frequency: "daily",
      hold_days: 2,
    };
  }

  addSuspiciousActivityFlag(flag: string): void {
    if (!this.suspicious_activity_flags) {
      this.suspicious_activity_flags = [];
    }
    if (!this.suspicious_activity_flags.includes(flag)) {
      this.suspicious_activity_flags.push(flag);
    }
  }

  removeSuspiciousActivityFlag(flag: string): void {
    if (this.suspicious_activity_flags) {
      this.suspicious_activity_flags = this.suspicious_activity_flags.filter((f) => f !== flag);
    }
  }
}
*/

// Mock Merchant class for when database is disabled
export class Merchant {
  merchant_id?: string;
  created_at?: Date;
  updated_at?: Date;
  entity_type?: number;
  legal_name?: string;
  business_name?: string;
  email?: string;
  verification_status?: number;
  status?: number;
}
