// ===================================================================
// MERCHANT NOTE REPOSITORY TEMPORARILY DISABLED
// ===================================================================
/*
import { EntityRepository, Repository } from "typeorm";
import { MerchantNote } from "../entity/merchant-note.entity.ts";

@EntityRepository(MerchantNote)
export class MerchantNoteRepository extends Repository<MerchantNote> {
  async findByMerchant(merchantId: string, includeInternal: boolean = true): Promise<MerchantNote[]> {
    const query: any = {
      merchant_id: merchantId,
    };

    if (!includeInternal) {
      query.is_internal = false;
    }

    const notes = await this.find({
      where: query,
      order: { created_at: "DESC" },
    });
    return notes ?? [];
  }

  async findByType(merchantId: string, noteType: number): Promise<MerchantNote[]> {
    const notes = await this.find({
      where: {
        merchant_id: merchantId,
        note_type: noteType,
      },
      order: { created_at: "DESC" },
    });
    return notes ?? [];
  }

  async createNote(
    merchantId: string,
    createdBy: string,
    noteText: string,
    noteType: number = 0,
    isInternal: boolean = true
  ): Promise<MerchantNote | undefined> {
    const note = new MerchantNote();
    note.merchant_id = merchantId;
    note.created_by = createdBy;
    note.note_text = noteText;
    note.note_type = noteType;
    note.is_internal = isInternal;

    const savedNote = await this.save(note);
    return savedNote ?? undefined;
  }
}
*/

// Mock repository for when database is disabled
export class MerchantNoteRepository {
  async findByMerchant(_merchantId: string, _includeInternal = true): Promise<never[]> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
