// ===================================================================
// MERCHANT AUDIT LOG REPOSITORY TEMPORARILY DISABLED
// ===================================================================
/*
import { EntityRepository, Repository } from "typeorm";
import { MerchantAuditLog } from "../entity/merchant-audit-log.entity.ts";

@EntityRepository(MerchantAuditLog)
export class MerchantAuditLogRepository extends Repository<MerchantAuditLog> {
  async findByMerchant(merchantId: string, limit: number = 50): Promise<MerchantAuditLog[]> {
    return this.find({
      where: { merchant_id: merchantId },
      order: { created_at: "DESC" },
      take: limit,
    });
  }

  async findByTable(merchantId: string, tableName: string): Promise<MerchantAuditLog[]> {
    return this.find({
      where: {
        merchant_id: merchantId,
        table_name: tableName,
      },
      order: { created_at: "DESC" },
    });
  }

  async findByRecordId(recordId: string): Promise<MerchantAuditLog[]> {
    return this.find({
      where: { record_id: recordId },
      order: { created_at: "DESC" },
    });
  }

  async findByDateRange(merchantId: string, startDate: Date, endDate: Date): Promise<MerchantAuditLog[]> {
    return this.createQueryBuilder("audit")
      .where("audit.merchant_id = :merchantId", { merchantId })
      .andWhere("audit.created_at >= :startDate", { startDate })
      .andWhere("audit.created_at <= :endDate", { endDate })
      .orderBy("audit.created_at", "DESC")
      .getMany();
  }

  async createLogEntry(
    merchantId: string,
    tableName: string,
    recordId: string,
    action: string,
    changedBy: string,
    changes: any,
    ipAddress?: string
  ): Promise<MerchantAuditLog | undefined> {
    const log = new MerchantAuditLog();
    log.merchant_id = merchantId;
    log.table_name = tableName;
    log.record_id = recordId;
    log.action = action;
    log.changed_by = changedBy;
    log.changes = changes;
    log.ip_address = ipAddress ?? "";

    const savedLog = await this.save(log);
    return savedLog ?? undefined;
  }
}
*/

// Mock repository for when database is disabled
export class MerchantAuditLogRepository {
  async findByMerchant(_merchantId: string, _limit = 50): Promise<never[]> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
