// ===================================================================
// MERCHANT BANK ACCOUNT REPOSITORY TEMPORARILY DISABLED
// ===================================================================
// To re-enable: uncomment all code below
// ===================================================================

/*
import { EntityRepository, Repository } from "typeorm";
import { MerchantBankAccount } from "../entity/merchant-bank-account.entity.ts";

@EntityRepository(MerchantBankAccount)
export class MerchantBankAccountRepository extends Repository<MerchantBankAccount> {
  async findPrimaryForMerchant(merchantId: string): Promise<MerchantBankAccount | undefined> {
    const account = await this.findOne({
      where: {
        merchant_id: merchantId,
        is_primary: true,
      },
    });
    return account ?? undefined;
  }

  async findAllForMerchant(merchantId: string): Promise<MerchantBankAccount[]> {
    return this.find({
      where: { merchant_id: merchantId },
      order: { is_primary: "DESC" },
    });
  }

  async updateStatus(accountId: string, status: number): Promise<void> {
    const updateData: any = {
      status,
      verification_date: status === 1 ? new Date() : undefined, // Set verification date if verified
    };

    await this.update(accountId, updateData);
  }

  async setAsPrimary(accountId: string, merchantId: string): Promise<void> {
    await this.manager.transaction(async (transactionalEntityManager) => {
      // First set all merchant accounts to non-primary
      await transactionalEntityManager.update(MerchantBankAccount, { merchant_id: merchantId }, { is_primary: false });

      // Then set the specific account as primary
      await transactionalEntityManager.update(MerchantBankAccount, { account_id: accountId }, { is_primary: true });
    });
  }
}
*/

// Mock repository for when database is disabled
export class MerchantBankAccountRepository {
  async findPrimaryForMerchant(_merchantId: string): Promise<undefined> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
