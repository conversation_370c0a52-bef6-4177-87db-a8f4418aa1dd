// ===================================================================
// MERCHANT MEMBER REPOSITORY TEMPORARILY DISABLED
// ===================================================================
// To re-enable: uncomment all code below
// ===================================================================

/*
import { EntityRepository, Repository, MoreThanOrEqual } from "typeorm";
import { MerchantMember } from "../entity/merchant-member.entity.ts";

@EntityRepository(MerchantMember)
export class MerchantMemberRepository extends Repository<MerchantMember> {
  async findPrimaryForMerchant(merchantId: string): Promise<MerchantMember | undefined> {
    const member = await this.findOne({
      where: {
        merchant_id: merchantId,
        is_primary: true,
      },
    });
    return member || undefined;
  }

  async findAllForMerchant(merchantId: string): Promise<MerchantMember[]> {
    return this.find({
      where: { merchant_id: merchantId },
      order: { is_primary: "DESC", ownership_percentage: "DESC" },
    });
  }

  async findByEmail(merchantId: string, email: string): Promise<MerchantMember | undefined> {
    const member = await this.findOne({
      where: {
        merchant_id: merchantId,
        email,
      },
    });
    return member || undefined;
  }

  async findByOwnershipThreshold(merchantId: string, minPercentage: number): Promise<MerchantMember[]> {
    return this.find({
      where: {
        merchant_id: merchantId,
        ownership_percentage: MoreThanOrEqual(minPercentage),
      },
      order: { ownership_percentage: "DESC" },
    });
  }

  async updateVerificationStatus(memberId: string, idVerificationStatus?: number, backgroundCheckStatus?: number): Promise<void> {
    const updateData: any = {};

    if (idVerificationStatus !== undefined) {
      updateData.id_verification_status = idVerificationStatus;
    }

    if (backgroundCheckStatus !== undefined) {
      updateData.background_check_status = backgroundCheckStatus;
    }

    if (Object.keys(updateData).length > 0) {
      await this.update(memberId, updateData);
    }
  }

  async setAsPrimary(memberId: string, merchantId: string): Promise<void> {
    await this.manager.transaction(async (transactionalEntityManager) => {
      // First set all merchant members to non-primary
      await transactionalEntityManager.update(MerchantMember, { merchant_id: merchantId }, { is_primary: false });

      // Then set the specific member as primary
      await transactionalEntityManager.update(MerchantMember, { member_id: memberId }, { is_primary: true });
    });
  }
}
*/

// Mock repository for when database is disabled
export class MerchantMemberRepository {
  async findPrimaryForMerchant(_merchantId: string): Promise<undefined> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
