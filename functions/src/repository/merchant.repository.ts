// ===================================================================
// MERCHANT REPOSITORY TEMPORARILY DISABLED
// ===================================================================
// To re-enable: uncomment all code below
// ===================================================================

/*
// src/repository/merchant.repository.ts
import { EntityRepository, Repository } from "typeorm";
import { Merchant } from "../entity/merchant.entity.ts";

@EntityRepository(Merchant)
export class MerchantRepository extends Repository<Merchant> {
  async findByEmail(email: string): Promise<Merchant | undefined> {
    const merchant = await this.findOne({ where: { email } });
    return merchant ?? undefined;
  }

  async findByStatus(status: number): Promise<Merchant[]> {
    return this.find({ where: { status } });
  }

  async findByVerificationStatus(verificationStatus: number): Promise<Merchant[]> {
    return this.find({ where: { verification_status: verificationStatus } });
  }

  async findWithFullDetails(merchantId: string): Promise<Merchant | undefined> {
    const merchant = await this.findOne({
      where: { merchant_id: merchantId },
      relations: ["bank_accounts", "members", "documents", "onboarding_status", "notes"],
    });
    return merchant ?? undefined;
  }

  async countByStatus(status: number): Promise<number> {
    return this.count({ where: { status } });
  }

  async updateStatus(merchantId: string, status: number, approvedBy?: string): Promise<void> {
    const updateData: any = { status };

    if (status === 2) {
      // Approved status
      updateData.approval_date = new Date();
      if (approvedBy) {
        updateData.approved_by = approvedBy;
      }
    }

    await this.update(merchantId, updateData);
  }
}
*/

// Mock repository for when database is disabled
export class MerchantRepository {
  async findByEmail(_email: string): Promise<undefined> {
    throw new Error("Database functionality is temporarily disabled");
  }

  async findByStatus(_status: number): Promise<never[]> {
    throw new Error("Database functionality is temporarily disabled");
  }

  async findWithFullDetails(_merchantId: string): Promise<undefined> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
