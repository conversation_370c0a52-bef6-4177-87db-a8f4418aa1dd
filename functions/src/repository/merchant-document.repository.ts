// ===================================================================
// MERCHANT DOCUMENT REPOSITORY TEMPORARILY DISABLED
// ===================================================================
/*
import { EntityRepository, Repository } from "typeorm";
import { MerchantDocument } from "../entity/merchant-document.entity.ts";

@EntityRepository(MerchantDocument)
export class MerchantDocumentRepository extends Repository<MerchantDocument> {
  async findByMerchant(merchantId: string): Promise<MerchantDocument[]> {
    return this.find({
      where: { merchant_id: merchantId },
      order: { created_at: "DESC" },
    });
  }

  async findByMember(memberId: string): Promise<MerchantDocument[]> {
    return this.find({
      where: { member_id: memberId },
      order: { created_at: "DESC" },
    });
  }

  async findByType(merchantId: string, documentType: number): Promise<MerchantDocument[]> {
    return this.find({
      where: {
        merchant_id: merchantId,
        document_type: documentType,
      },
      order: { created_at: "DESC" },
    });
  }

  async updateVerificationStatus(documentId: string, status: number, verifiedBy?: string): Promise<void> {
    const updateData: any = {
      verification_status: status,
      verification_date: new Date(),
    };

    if (verifiedBy) {
      updateData.verified_by = verifiedBy;
    }

    await this.update(documentId, updateData);
  }

  async countByVerificationStatus(merchantId: string, status: number): Promise<number> {
    return this.count({
      where: {
        merchant_id: merchantId,
        verification_status: status,
      },
    });
  }
}
*/

// Mock repository for when database is disabled
export class MerchantDocumentRepository {
  async findByMerchant(_merchantId: string): Promise<never[]> {
    throw new Error("Database functionality is temporarily disabled");
  }
}
