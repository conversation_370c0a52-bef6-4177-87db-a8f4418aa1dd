import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createIframeResponse } from "../helpers/response.js";

interface SecurityValidationResult {
  isValid: boolean;
  error?: string;
  statusCode?: number;
}

export const validateIframeSecurity = (event: APIGatewayProxyEvent): SecurityValidationResult => {
  // Rate limiting check (basic implementation)
  const userAgent = event.headers["User-Agent"] || event.headers["user-agent"] || "";
  const sourceIp = event.requestContext.identity.sourceIp;

  // Block suspicious user agents
  const suspiciousPatterns = [/bot/i, /crawler/i, /spider/i, /scraper/i, /curl/i, /wget/i];

  if (suspiciousPatterns.some((pattern) => pattern.test(userAgent))) {
    console.warn("Blocked suspicious user agent:", { userAgent, sourceIp });
    return {
      isValid: false,
      error: "Access denied",
      statusCode: 403,
    };
  }

  // Validate content type for POST requests
  if (event.httpMethod === "POST") {
    const contentType = event.headers["Content-Type"] || event.headers["content-type"] || "";
    if (!contentType.includes("application/json")) {
      return {
        isValid: false,
        error: "Invalid content type. Expected application/json",
        statusCode: 400,
      };
    }
  }

  // Validate request size (prevent large payloads)
  const contentLength = parseInt(event.headers["Content-Length"] || event.headers["content-length"] || "0");
  const maxSize = 10 * 1024; // 10 * 1024; // 10 MB

  if (contentLength > maxSize) {
    return {
      isValid: false,
      error: "Request payload too large",
      statusCode: 413,
    };
  }

  return { isValid: true };
};

export const validateTokenFormat = (token: string): SecurityValidationResult => {
  // Token should be a hex string of specific length (64 characters for 32 bytes)
  const tokenPattern = /^[a-f0-9]{64}$/i;

  if (!token) {
    return {
      isValid: false,
      error: "Token is required",
      statusCode: 400,
    };
  }

  if (!tokenPattern.test(token)) {
    return {
      isValid: false,
      error: "Invalid token format",
      statusCode: 400,
    };
  }

  return { isValid: true };
};

export const validateMerchantIdFormat = (merchantId: string): SecurityValidationResult => {
  // Merchant ID should be alphanumeric and reasonable length
  const merchantIdPattern = /^[a-zA-Z0-9_-]{1,50}$/;

  if (!merchantId) {
    return {
      isValid: false,
      error: "Merchant ID is required",
      statusCode: 400,
    };
  }

  if (!merchantIdPattern.test(merchantId)) {
    return {
      isValid: false,
      error: "Invalid merchant ID format",
      statusCode: 400,
    };
  }

  return { isValid: true };
};

export const validateAmount = (amount: number): SecurityValidationResult => {
  const minAmount = 1; // 1 cent
  const maxAmount = 100000000; // $1,000,000

  if (amount < minAmount) {
    return {
      isValid: false,
      error: "Amount must be at least 1 cent",
      statusCode: 400,
    };
  }

  if (amount > maxAmount) {
    return {
      isValid: false,
      error: "Amount exceeds maximum allowed",
      statusCode: 400,
    };
  }

  return { isValid: true };
};

export const validateReturnUrl = (url: string): SecurityValidationResult => {
  try {
    const parsedUrl = new URL(url);

    // Only allow HTTPS URLs (except localhost for development)
    if (parsedUrl.protocol !== "https:" && !parsedUrl.hostname.includes("localhost")) {
      return {
        isValid: false,
        error: "Return URL must use HTTPS",
        statusCode: 400,
      };
    }

    // Block suspicious domains
    const blockedDomains = ["bit.ly", "tinyurl.com", "short.link", "t.co"];

    if (blockedDomains.some((domain) => parsedUrl.hostname.includes(domain))) {
      return {
        isValid: false,
        error: "URL shorteners are not allowed",
        statusCode: 400,
      };
    }

    return { isValid: true };
  } catch (error) {
    console.error("Error validating return URL:", error);
    return {
      isValid: false,
      error: "Invalid URL format",
      statusCode: 400,
    };
  }
};

export const withIframeSecurity = (handler: (event: APIGatewayProxyEvent) => Promise<APIGatewayProxyResult>) => {
  return async (event: APIGatewayProxyEvent) => {
    // Validate basic security
    const securityValidation = validateIframeSecurity(event);
    if (!securityValidation.isValid) {
      return createIframeResponse(securityValidation.statusCode || 403, {
        error: securityValidation.error,
        message: "Security validation failed",
      });
    }

    // Log security-relevant information
    console.log("Iframe request security info:", {
      sourceIp: event.requestContext.identity.sourceIp,
      userAgent: event.headers["User-Agent"] || event.headers["user-agent"],
      referer: event.headers["Referer"] || event.headers["referer"],
      origin: event.headers["Origin"] || event.headers["origin"],
      timestamp: new Date().toISOString(),
    });

    // Call the original handler
    return handler(event);
  };
};

export const sanitizeInput = (input: string): string => {
  if (typeof input !== "string") {
    return "";
  }

  return input
    .replace(/[<>]/g, "") // Remove potential HTML tags
    .replace(/['"]/g, "") // Remove quotes
    .replace(/[\\]/g, "") // Remove backslashes
    .trim()
    .substring(0, 1000); // Limit length
};

export const validateDescription = (description: string): SecurityValidationResult => {
  const sanitized = sanitizeInput(description);

  if (!sanitized || sanitized.length < 1) {
    return {
      isValid: false,
      error: "Description is required",
      statusCode: 400,
    };
  }

  if (sanitized.length > 255) {
    return {
      isValid: false,
      error: "Description too long (max 255 characters)",
      statusCode: 400,
    };
  }

  return { isValid: true };
};
